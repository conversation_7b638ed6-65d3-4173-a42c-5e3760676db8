import React, { useState, useEffect } from 'react';
import <PERSON>rrorLogsViewer from './ErrorLogsViewer';
import Header from '../utils/header';
import Footer from '../utils/footer';

const ProtectedErrorLogsViewer = () => {
    const [isAuthorized, setIsAuthorized] = useState(false);
    const [isLoading, setIsLoading] = useState(true);
    const [userLevel, setUserLevel] = useState(null);

    useEffect(() => {
        checkAuthorization();
    }, []);

    const checkAuthorization = () => {
        try {
            // Verificar si hay token de autenticación
            const token = localStorage.getItem('apitoken_rciplatform');
            const userLvl = localStorage.getItem('rciplatform_userlvl');

            if (!token) {
                // No hay token, redirigir al login
                window.location.href = '/';
                return;
            }

            setUserLevel(userLvl);

            // Verificar nivel de usuario
            // Nivel 1 = admin (basado en el header que muestra más opciones para nivel != 3)
            if (userLvl === '1') {
                setIsAuthorized(true);
            } else {
                setIsAuthorized(false);
            }

            setIsLoading(false);
        } catch (error) {
            console.error('Error checking authorization:', error);
            setIsLoading(false);
            setIsAuthorized(false);
        }
    };

    if (isLoading) {
        return (
            <div style={{ 
                display: 'flex', 
                justifyContent: 'center', 
                alignItems: 'center', 
                height: '100vh',
                flexDirection: 'column'
            }}>
                <div style={{ fontSize: '18px', marginBottom: '10px' }}>
                    Verificando permisos...
                </div>
                <div style={{ 
                    width: '40px', 
                    height: '40px', 
                    border: '4px solid #f3f3f3',
                    borderTop: '4px solid #3498db',
                    borderRadius: '50%',
                    animation: 'spin 1s linear infinite'
                }}>
                </div>
                <style>{`
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                `}</style>
            </div>
        );
    }

    if (!isAuthorized) {
        return (
            <div>
                <Header />
                <div style={{ 
                    padding: '40px', 
                    textAlign: 'center',
                    minHeight: '60vh',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center'
                }}>
                    <div style={{ 
                        backgroundColor: '#f8d7da',
                        color: '#721c24',
                        padding: '20px',
                        borderRadius: '8px',
                        border: '1px solid #f5c6cb',
                        maxWidth: '500px'
                    }}>
                        <h2 style={{ margin: '0 0 15px 0' }}>
                            🚫 Acceso Denegado
                        </h2>
                        <p style={{ margin: '0 0 15px 0' }}>
                            No tienes permisos para acceder a esta sección.
                        </p>
                        <p style={{ margin: '0 0 20px 0', fontSize: '14px' }}>
                            Nivel de usuario actual: {userLevel || 'No definido'}
                        </p>
                        <button 
                            onClick={() => window.location.href = '/reserve'}
                            style={{
                                backgroundColor: '#007bff',
                                color: 'white',
                                border: 'none',
                                padding: '10px 20px',
                                borderRadius: '4px',
                                cursor: 'pointer'
                            }}
                        >
                            Volver al Dashboard
                        </button>
                    </div>
                </div>
                <Footer />
            </div>
        );
    }

    return (
        <div>
            <Header />
            <div style={{ minHeight: '80vh' }}>
                <ErrorLogsViewer />
            </div>
            <Footer />
        </div>
    );
};

export default ProtectedErrorLogsViewer;
