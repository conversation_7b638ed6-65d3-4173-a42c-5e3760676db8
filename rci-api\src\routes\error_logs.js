const express = require("express");
const router = express.Router();
const errorLogsController = require("../controllers/error_logs_controller");

// Middleware de autenticación (opcional - ajustar según tu sistema)
const authenticateAdmin = (req, res, next) => {
    // Aquí puedes agregar tu lógica de autenticación
    // Por ejemplo, verificar JWT token para admin
    next();
};

// Rutas para error logs
router.post("/error-logs", errorLogsController.saveErrorLog);
router.get("/error-logs", authenticateAdmin, errorLogsController.getErrorLogs);
router.get("/error-logs/stats", authenticateAdmin, errorLogsController.getErrorStats);
router.delete("/error-logs/cleanup", authenticateAdmin, errorLogsController.cleanOldLogs);

module.exports = router;
