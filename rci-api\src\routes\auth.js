// Express
const express = require("express");

// Controllers
const { login } = require("../controllers/auth_controller");
const verifyToken = require("../controllers/verifyToken");

const Auth = express.Router();

/** Users Routes */
Auth.post("/login", login);

Auth.get("/verifytoken", verifyToken, (req, res) => {
    res.status(200).json({
        status: 200,
        auth: true,
        id_user: req.userId,
    });
});

module.exports = Auth;