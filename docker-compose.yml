version: '3.8'
services:
  mysql:
    image: mysql:8.0
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 0
      MYSQL_DATABASE: transrou_rci-dev
      MYSQL_ROOT_HOST: '%'
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql-init:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p0"]
      timeout: 20s
      retries: 10

  frontend:
    build:
      context: ./rci-booking-form
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    working_dir: /app
    volumes:
      - ./rci-booking-form:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - CHOKIDAR_USEPOLLING=true
      - WATCHPACK_POLLING=true
    command: ["npm", "start"]
    depends_on:
      backend:
        condition: service_started
      mysql:
        condition: service_healthy

  backend:
    build:
      context: ./rci-api
      dockerfile: Dockerfile
    ports:
      - "4000:4000"
    working_dir: /app
    volumes:
      - ./rci-api:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - CHOKIDAR_USEPOLLING=true
      - WATCHPACK_POLLING=true
    depends_on:
      mysql:
        condition: service_healthy
    command: ["npx", "nodemon", "app.js"]
volumes:
  mysql_data:
