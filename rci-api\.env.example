# Configuración de base de datos de producción (para seed automático)
PROD_DB_HOST=tu-servidor-produccion.com
PROD_DB_USER=tu-usuario-produccion
PROD_DB_PASSWORD=tu-password-produccion
PROD_DB_NAME=transrou_rci-reservations

# Configuración de base de datos de desarrollo (local)
DEV_DB_HOST=mysql
DEV_DB_USER=root
DEV_DB_PASSWORD=root
DEV_DB_NAME=transrou_rci-dev

# Entorno de ejecución
ENVIRONMENT=DEVELOPMENT

# Puerto del servidor API
PORT=4000

# Configuración adicional
NODE_ENV=development
