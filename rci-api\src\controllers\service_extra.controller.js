const { pool } = require('../database'); // Se utiliza pool en lugar de db
const moment = require("moment"); // Agregado para formatear fechas

exports.get = async (req, res) => {
    try {
        const baseQuery = `SELECT ID, ID_RESERVATION, NOMBRE, CORREO, TRANSPORTE, SERVICIO_EXTRA FROM RCI_CLIENTES_PRUEBAS WHERE SERVICIO_EXTRA IS NOT NULL`;
        let query = baseQuery;
        let params = [];
        
        if (req.query.date_init && req.query.date_end) {
            const filter = req.query.filter || "default";
            let column;
            switch (filter) {
                case "arrival":
                    column = "FECHA_LLEGADA";
                    break;
                case "departure":
                    column = "FECHA_SALIDA";
                    break;
                default:
                    column = "FECHA_REGISTRO";
                    break;
            }
            const initDate = parseDate(req.query.date_init);
            const endDate = parseDate(req.query.date_end);
            query = `${baseQuery} AND STR_TO_DATE(${column}, '%m/%d/%Y') BETWEEN STR_TO_DATE(?, '%m/%d/%Y') AND STR_TO_DATE(?, '%m/%d/%Y')`;
            params.push(initDate, endDate);
        } else {
            // Si no se proporcionan argumentos de fecha o filtro, se usa la consulta base.
            query = baseQuery;
        }
        
        const [rows] = await pool.promise().query(query, params);
        res.status(200).json({ results: rows });
    } catch (error) {
        console.error('Error al obtener registros con SERVICIO_EXTRA:', error);
        res.status(500).json({ error: "Error interno del servidor" });
    }
};

// Función para parser de fecha similar al del controlador reservation_controller
const parseDate = (date) => {
    return moment(date, "DD/MM/YYYY").format("MM/DD/YYYY");
};

exports.getById = async (req, res) => {
    try {
        const { id } = req.params;
        const query = `SELECT * FROM RCI_CLIENTES_PRUEBAS WHERE SERVICIO_EXTRA IS NOT NULL AND ID = ?`;
        const [rows] = await pool.promise().query(query, [id]);
        if (rows.length > 0) {
            res.status(200).json({ result: rows[0] });
        } else {
            res.status(404).json({ message: "Registro no encontrado" });
        }
    } catch (error) {
        console.error('Error al obtener registro con ID_RESERVATION y SERVICIO_EXTRA:', error);
        res.status(500).json({ error: "Error interno del servidor" });
    }
};