#!/bin/bash

# Script para ejecutar seed automático después de crear la estructura
# Solo se ejecuta una vez cuando se crea el contenedor MySQL

echo "🌱 Iniciando seed automático de datos..."

# Esperar a que el backend esté disponible (máximo 60 segundos)
for i in {1..12}; do
    if curl -s http://backend:4000/ > /dev/null 2>&1; then
        echo "✅ Backend disponible, ejecutando seed..."
        
        # Ejecutar seed
        curl -X POST http://backend:4000/api/v1/seed-db
        
        if [ $? -eq 0 ]; then
            echo "🎉 Seed completado exitosamente"
        else
            echo "❌ Error en el seed"
        fi
        break
    else
        echo "⏳ Esperando backend... ($i/12)"
        sleep 5
    fi
done

echo "✅ Inicialización de MySQL completada"
