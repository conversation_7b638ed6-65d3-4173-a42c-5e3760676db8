const { pool } = require('../database');
const mysql = require('mysql2/promise');

// Configuración para conectar a la base de datos de producción
const prodConfig = {
    host: process.env.DB_HOST,
    database: process.env.DB_NAME,
    user: process.env.DB_USER,
    password: process.env.DB_PASS,
    waitForConnections: true,
    connectionLimit: 5,
    acquireTimeout: 60000,
    timeout: 60000,
};

// Tablas a respaldar con sus nombres reales (basado en los controladores)
const TABLES = [
    'USUARIOS',                // users_controller
    'RCI_CLIENTES_PRUEBAS',   // reservation_controller
    'CONTROL_TARIFAS',        // rates_control_controller
    'RATES',                  // rate_controller
    'ERROR_LOGS',             // error_logs_controller
    // Agrega más tablas según sea necesario
];

// Configuración de filtros por tabla
const TABLE_FILTERS = {
    'RCI_CLIENTES_PRUEBAS': {
        orderColumn: 'ID_RESERVATION', // Columna autoincremental para ordenar
        limitRecords: 500, // Limitar a los últimos 100 registros
        orderDirection: 'DESC' // Descendente para obtener los más recientes
    },
    'CONTROL_TARIFAS': {
        orderColumn: 'ID_CONTROL_TARIFAS', // Columna autoincremental para ordenar
        limitRecords: 500, // Limitar a los últimos 100 registros
        orderDirection: 'DESC' // Descendente para obtener los más recientes
    }
    // Se pueden agregar más filtros para otras tablas aquí
};

// Función para normalizar datos de tabla y asegurar consistencia
function normalizeTableData(rows, tableName) {
    if (!rows || rows.length === 0) return rows;

    // Obtener todas las columnas posibles de todos los registros
    const allColumns = new Set();
    rows.forEach(row => {
        Object.keys(row).forEach(col => allColumns.add(col));
    });

    const columnsList = Array.from(allColumns);

    // Normalizar cada registro para que tenga todas las columnas
    const normalizedRows = rows.map(row => {
        const normalizedRow = {};
        columnsList.forEach(col => {
            normalizedRow[col] = row.hasOwnProperty(col) ? row[col] : null;
        });
        return normalizedRow;
    });

    return normalizedRows;
}

// Función auxiliar para generar consulta SQL con filtros
const generateSelectQuery = (table) => {
    const filter = TABLE_FILTERS[table];

    if (!filter) {
        // Sin filtro, seleccionar todos los registros
        return `SELECT * FROM \`${table}\``;
    }

    // Si tiene límite de registros, usar LIMIT
    if (filter.limitRecords) {
        if (filter.orderColumn) {
            // Ordenar por columna específica (ej: ID_RESERVATION) y limitar registros
            const direction = filter.orderDirection || 'DESC';
            return `SELECT * FROM \`${table}\` ORDER BY ${filter.orderColumn} ${direction} LIMIT ${filter.limitRecords}`;
        } else if (filter.dateColumn) {
            // Ordenar por fecha y limitar registros (compatibilidad)
            return `SELECT * FROM \`${table}\` ORDER BY ${filter.dateColumn} DESC LIMIT ${filter.limitRecords}`;
        } else {
            // Solo limitar registros sin orden específico
            return `SELECT * FROM \`${table}\` LIMIT ${filter.limitRecords}`;
        }
    }

    // Lógica anterior para filtros por fecha (mantener compatibilidad)
    if (filter.yearsBack && filter.dateColumn) {
        // Calcular la fecha límite (X años hacia atrás desde hoy)
        const currentDate = new Date();
        const limitDate = new Date();
        limitDate.setFullYear(currentDate.getFullYear() - filter.yearsBack);

        // Formatear la fecha límite al formato de la base de datos (día/mes/año)
        const formattedLimitDate = `${limitDate.getDate().toString().padStart(2, '0')}/${(limitDate.getMonth() + 1).toString().padStart(2, '0')}/${limitDate.getFullYear()}`;

        // Generar consulta con filtro de fecha
        const query = `
            SELECT * FROM \`${table}\`
            WHERE STR_TO_DATE(${filter.dateColumn}, '${filter.dateFormat}') >= STR_TO_DATE('${formattedLimitDate}', '${filter.dateFormat}')
            ORDER BY ${filter.dateColumn} DESC
        `;

        return query.trim();
    }

    return `SELECT * FROM \`${table}\``;
};

exports.seedDatabase = async (req, res) => {
    // Verificar que solo se ejecute en desarrollo
    if (process.env.ENVIRONMENT !== 'DEVELOPMENT') {
        return res.status(403).json({
            success: false,
            error: 'Seed solo permitido en entorno de desarrollo.'
        });
    }

    let prodConn;
    const results = {
        success: true,
        tables: {},
        totalRecords: 0,
        errors: []
    };

    try {
        console.log('🌱 Iniciando seed de la base de datos...');
        console.log('📡 Conectando a la base de datos de producción...');

        // Conectar a la base de datos de producción
        prodConn = await mysql.createConnection(prodConfig);
        console.log('✅ Conexión a producción establecida');

        // Verificar conexión a la base local
        await pool.promise().query('SELECT 1');
        console.log('✅ Conexión a base local verificada');

        // Procesar cada tabla
        for (const table of TABLES) {
            try {
                console.log(`\n📋 Procesando tabla: ${table}`);

                // 1. Verificar si la tabla existe en producción
                const [tableExists] = await prodConn.query(
                    `SELECT COUNT(*) as count FROM information_schema.tables
                     WHERE table_schema = ? AND table_name = ?`,
                    [process.env.DB_NAME, table]
                );

                if (tableExists[0].count === 0) {
                    console.log(`⚠️  Tabla ${table} no existe en producción, saltando...`);
                    results.tables[table] = {
                        status: 'skipped',
                        reason: 'No existe en producción',
                        records: 0
                    };
                    continue;
                }

                // 2. Verificar si la tabla existe en local
                const [localTableExists] = await pool.promise().query(
                    `SELECT COUNT(*) as count FROM information_schema.tables
                     WHERE table_schema = ? AND table_name = ?`,
                    [process.env.DEV_DB_NAME, table]
                );

                if (localTableExists[0].count === 0) {
                    console.log(`⚠️  Tabla ${table} no existe en local, saltando...`);
                    results.tables[table] = {
                        status: 'skipped',
                        reason: 'No existe en base local',
                        records: 0
                    };
                    continue;
                }

                // 3. Obtener datos de producción con filtros si aplican
                const selectQuery = generateSelectQuery(table);
                const filter = TABLE_FILTERS[table];

                if (filter) {
                    console.log(`📥 Obteniendo datos de ${table} (últimos ${filter.yearsBack} años)...`);
                    console.log(`🔍 Filtro aplicado: ${filter.dateColumn} >= hace ${filter.yearsBack} años`);
                } else {
                    console.log(`📥 Obteniendo datos de ${table} (todos los registros)...`);
                }

                const [rows] = await prodConn.query(selectQuery);
                console.log(`📊 Encontrados ${rows.length} registros en ${table}`);

                // 4. Limpiar tabla local
                console.log(`🧹 Limpiando tabla local ${table}...`);
                await pool.promise().query(`DELETE FROM \`${table}\``);

                // 5. Insertar datos si existen
                if (rows.length > 0) {
                    console.log(`💾 Insertando ${rows.length} registros en ${table}...`);

                    // Obtener la estructura de la tabla local para usar como referencia
                    const [tableStructure] = await pool.promise().query(`DESCRIBE \`${table}\``);
                    const tableColumns = tableStructure.map(col => col.Field);

                    console.log(`🏗️  Usando estructura de tabla con ${tableColumns.length} columnas`);

                    // Normalizar datos usando la estructura de la tabla
                    const normalizedRows = rows.map(row => {
                        const normalizedRow = {};
                        tableColumns.forEach(col => {
                            normalizedRow[col] = row.hasOwnProperty(col) ? row[col] : null;
                        });
                        return normalizedRow;
                    });

                    // Logging adicional para RCI_CLIENTES_PRUEBAS
                    if (table === 'RCI_CLIENTES_PRUEBAS') {
                        console.log(`🔍 Verificando estructura de ${table}...`);
                        console.log(`🔑 Columnas de la tabla (${tableColumns.length}):`, tableColumns);

                        // Verificar si hay registros con diferente número de columnas
                        const inconsistentRows = rows.filter(row => Object.keys(row).length !== tableColumns.length);
                        if (inconsistentRows.length > 0) {
                            console.log(`⚠️  Encontrados ${inconsistentRows.length} registros con número inconsistente de columnas`);
                            console.log(`🔧 Normalizando ${rows.length} registros usando estructura de tabla...`);
                        }
                    }

                    // Para RCI_CLIENTES_PRUEBAS, insertar uno por uno para identificar problemas
                    if (table === 'RCI_CLIENTES_PRUEBAS') {
                        console.log(`🔧 Insertando registros uno por uno para identificar problemas...`);
                        let successCount = 0;
                        let errorCount = 0;

                        for (let i = 0; i < normalizedRows.length; i++) {
                            try {
                                const row = normalizedRows[i];

                                // Procesar valores especiales (JSON, strings con caracteres especiales, etc.)
                                const values = tableColumns.map(col => {
                                    let value = row[col];

                                    // Si es null o undefined, convertir a null
                                    if (value === undefined || value === '') {
                                        return null;
                                    }

                                    // Si es la columna SERVICIO_EXTRA y contiene un objeto, convertirlo a JSON string
                                    if (col === 'SERVICIO_EXTRA' && value && typeof value === 'object') {
                                        value = JSON.stringify(value);
                                    }

                                    // Si es un string, limpiar caracteres problemáticos
                                    if (typeof value === 'string') {
                                        // Escapar caracteres especiales que pueden causar problemas
                                        value = value.replace(/\0/g, '\\0')
                                                    .replace(/\n/g, '\\n')
                                                    .replace(/\r/g, '\\r')
                                                    .replace(/\x1a/g, '\\Z');
                                    }

                                    return value;
                                });

                                const placeholders = tableColumns.map(() => '?').join(',');
                                const sql = `INSERT INTO \`${table}\` (${tableColumns.map(col => `\`${col}\``).join(',')}) VALUES (${placeholders})`;

                                await pool.promise().query(sql, values);
                                successCount++;

                                if ((i + 1) % 10 === 0) {
                                    console.log(`📊 Progreso: ${i + 1}/${normalizedRows.length} registros procesados`);
                                }
                            } catch (rowError) {
                                errorCount++;
                                console.error(`❌ Error en registro ${i + 1}:`, rowError.message);

                                // Mostrar información específica sobre SERVICIO_EXTRA
                                const problematicRow = normalizedRows[i];
                                console.log(`🔍 ID_RESERVATION:`, problematicRow.ID_RESERVATION);
                                console.log(`🔍 SERVICIO_EXTRA:`, problematicRow.SERVICIO_EXTRA);
                                console.log(`🔍 Tipo de SERVICIO_EXTRA:`, typeof problematicRow.SERVICIO_EXTRA);
                                console.log(`🔍 Longitud de SERVICIO_EXTRA:`, problematicRow.SERVICIO_EXTRA ? problematicRow.SERVICIO_EXTRA.length : 'NULL');

                                // Mostrar solo las primeras columnas para debug
                                const debugData = {
                                    ID_RESERVATION: problematicRow.ID_RESERVATION,
                                    NOMBRE: problematicRow.NOMBRE,
                                    APELLIDO: problematicRow.APELLIDO,
                                    SERVICIO_EXTRA: problematicRow.SERVICIO_EXTRA,
                                    PICKUP_AT_HOTEL: problematicRow.PICKUP_AT_HOTEL
                                };
                                console.log(`🔍 Datos clave del registro:`, JSON.stringify(debugData, null, 2));

                                // Continuar con el siguiente registro
                                continue;
                            }
                        }

                        console.log(`✅ ${table}: ${successCount} registros insertados correctamente`);
                        if (errorCount > 0) {
                            console.log(`⚠️  ${table}: ${errorCount} registros fallaron`);
                        }

                        results.tables[table] = {
                            status: errorCount > 0 ? 'partial' : 'success',
                            records: successCount,
                            errors: errorCount
                        };
                        results.totalRecords += successCount;
                    } else {
                        // Para otras tablas, usar inserción masiva
                        const values = normalizedRows.map(row => tableColumns.map(col => row[col]));
                        const placeholders = values.map(() => `(${tableColumns.map(() => '?').join(',')})`).join(',');
                        const sql = `INSERT INTO \`${table}\` (${tableColumns.map(col => `\`${col}\``).join(',')}) VALUES ${placeholders}`;

                        await pool.promise().query(sql, values.flat());
                        console.log(`✅ ${table}: ${rows.length} registros insertados correctamente`);

                        results.tables[table] = {
                            status: 'success',
                            records: rows.length
                        };
                        results.totalRecords += rows.length;
                    }
                } else {
                    console.log(`ℹ️  ${table}: Tabla vacía en producción`);
                    results.tables[table] = {
                        status: 'empty',
                        records: 0
                    };
                }

            } catch (tableError) {
                console.error(`❌ Error procesando tabla ${table}:`, tableError.message);
                results.errors.push({
                    table: table,
                    error: tableError.message
                });
                results.tables[table] = {
                    status: 'error',
                    error: tableError.message,
                    records: 0
                };
            }
        }

        console.log('\n🎉 Seed completado!');
        console.log(`📊 Total de registros copiados: ${results.totalRecords}`);

        if (results.errors.length > 0) {
            results.success = false;
            console.log(`⚠️  Se encontraron ${results.errors.length} errores`);
        }

        res.json({
            success: results.success,
            message: `Seed completado. ${results.totalRecords} registros copiados.`,
            details: results
        });

    } catch (err) {
        console.error('❌ Error general en seed:', err);
        res.status(500).json({
            success: false,
            error: 'Error al hacer seed de la base de datos.',
            details: err.message,
            results: results
        });
    } finally {
        if (prodConn) {
            await prodConn.end();
            console.log('🔌 Conexión a producción cerrada');
        }
    }
};

// Función para obtener información de las tablas disponibles
exports.getTablesInfo = async (req, res) => {
    if (process.env.ENVIRONMENT !== 'DEVELOPMENT') {
        return res.status(403).json({
            success: false,
            error: 'Información de tablas solo disponible en desarrollo.'
        });
    }

    let prodConn;
    try {
        // Conectar a producción
        prodConn = await mysql.createConnection(prodConfig);

        const tablesInfo = {
            production: {},
            local: {},
            configured: TABLES,
            filters: TABLE_FILTERS
        };

        // Información de tablas en producción
        for (const table of TABLES) {
            try {
                const [prodCount] = await prodConn.query(`SELECT COUNT(*) as count FROM \`${table}\``);
                tablesInfo.production[table] = {
                    exists: true,
                    records: prodCount[0].count
                };
            } catch (err) {
                tablesInfo.production[table] = {
                    exists: false,
                    error: err.message
                };
            }
        }

        // Información de tablas en local
        for (const table of TABLES) {
            try {
                const [localCount] = await pool.promise().query(`SELECT COUNT(*) as count FROM \`${table}\``);
                tablesInfo.local[table] = {
                    exists: true,
                    records: localCount[0].count
                };
            } catch (err) {
                tablesInfo.local[table] = {
                    exists: false,
                    error: err.message
                };
            }
        }

        res.json({
            success: true,
            data: tablesInfo
        });

    } catch (err) {
        console.error('Error obteniendo información de tablas:', err);
        res.status(500).json({
            success: false,
            error: 'Error al obtener información de tablas.',
            details: err.message
        });
    } finally {
        if (prodConn) await prodConn.end();
    }
};

// Función para limpiar todas las tablas locales
exports.clearLocalTables = async (req, res) => {
    if (process.env.ENVIRONMENT !== 'DEVELOPMENT') {
        return res.status(403).json({
            success: false,
            error: 'Limpieza de tablas solo permitida en desarrollo.'
        });
    }

    try {
        const results = {};

        for (const table of TABLES) {
            try {
                const [result] = await pool.promise().query(`DELETE FROM \`${table}\``);
                results[table] = {
                    success: true,
                    deletedRows: result.affectedRows
                };
                console.log(`🧹 Tabla ${table} limpiada: ${result.affectedRows} registros eliminados`);
            } catch (err) {
                results[table] = {
                    success: false,
                    error: err.message
                };
                console.error(`❌ Error limpiando tabla ${table}:`, err.message);
            }
        }

        res.json({
            success: true,
            message: 'Limpieza de tablas completada',
            results: results
        });

    } catch (err) {
        console.error('Error en limpieza de tablas:', err);
        res.status(500).json({
            success: false,
            error: 'Error al limpiar tablas.',
            details: err.message
        });
    }
};

// Función para crear las tablas locales con la misma estructura que producción
exports.createTablesFromProduction = async (req, res) => {
    if (process.env.ENVIRONMENT !== 'DEVELOPMENT') {
        return res.status(403).json({
            success: false,
            error: 'Creación de tablas solo permitida en desarrollo.'
        });
    }

    let prodConn;
    const results = {
        success: true,
        tables: {},
        errors: []
    };

    try {
        console.log('🏗️  Iniciando creación de tablas desde producción...');

        // Conectar a producción
        prodConn = await mysql.createConnection(prodConfig);
        console.log('✅ Conexión a producción establecida');

        for (const table of TABLES) {
            try {
                console.log(`\n📋 Procesando estructura de tabla: ${table}`);

                // Verificar si la tabla existe en producción
                const [tableExists] = await prodConn.query(
                    `SELECT COUNT(*) as count FROM information_schema.tables
                     WHERE table_schema = ? AND table_name = ?`,
                    [process.env.DB_NAME, table]
                );

                if (tableExists[0].count === 0) {
                    console.log(`⚠️  Tabla ${table} no existe en producción, saltando...`);
                    results.tables[table] = {
                        status: 'skipped',
                        reason: 'No existe en producción'
                    };
                    continue;
                }

                // Obtener la estructura de la tabla de producción
                const [createTableResult] = await prodConn.query(`SHOW CREATE TABLE \`${table}\``);
                const createTableSQL = createTableResult[0]['Create Table'];

                console.log(`📝 Estructura obtenida para ${table}`);

                // Verificar si la tabla ya existe en local
                const [localTableExists] = await pool.promise().query(
                    `SELECT COUNT(*) as count FROM information_schema.tables
                     WHERE table_schema = ? AND table_name = ?`,
                    [process.env.DEV_DB_NAME, table]
                );

                if (localTableExists[0].count > 0) {
                    console.log(`🗑️  Eliminando tabla existente ${table} en local...`);
                    await pool.promise().query(`DROP TABLE \`${table}\``);
                }

                // Crear la tabla en local con la misma estructura
                console.log(`🏗️  Creando tabla ${table} en local...`);
                await pool.promise().query(createTableSQL);

                console.log(`✅ Tabla ${table} creada exitosamente`);
                results.tables[table] = {
                    status: 'created'
                };

            } catch (tableError) {
                console.error(`❌ Error procesando tabla ${table}:`, tableError.message);
                results.errors.push({
                    table: table,
                    error: tableError.message
                });
                results.tables[table] = {
                    status: 'error',
                    error: tableError.message
                };
                results.success = false;
            }
        }

        console.log('\n🎉 Creación de tablas completada!');

        if (results.errors.length > 0) {
            console.log(`⚠️  Se encontraron ${results.errors.length} errores`);
        }

        res.json({
            success: results.success,
            message: `Creación de tablas completada.`,
            details: results
        });

    } catch (err) {
        console.error('❌ Error general creando tablas:', err);
        res.status(500).json({
            success: false,
            error: 'Error al crear tablas.',
            details: err.message,
            results: results
        });
    } finally {
        if (prodConn) {
            await prodConn.end();
            console.log('🔌 Conexión a producción cerrada');
        }
    }
};

// Endpoint para comparar estructuras de tablas
exports.compareTableStructures = async (req, res) => {
    try {
        const tableName = req.params.tableName || 'RCI_CLIENTES_PRUEBAS';

        // Obtener estructura de desarrollo usando pool.query
        const devStructure = await new Promise((resolve, reject) => {
            pool.query(`DESCRIBE ${tableName}`, (error, results) => {
                if (error) reject(error);
                else resolve(results);
            });
        });

        // Estructura esperada de producción (basada en el archivo SQL)
        const expectedColumns = [
            'ID_RESERVATION', 'ID', 'NOMBRE', 'APELLIDO', 'CORREO', 'TELEFONO',
            'CIUDAD', 'ESTADO', 'PAIS', 'FECHA_REGISTRO', 'FECHA_LLEGADA',
            'HORA_IN', 'VUELO_IN', 'AEROLINEA_IN', 'FECHA_SALIDA', 'HORA_OUT',
            'VUELO_OUT', 'AEROLINEA_OUT', 'HOTEL', 'ZONA', 'TRANSPORTE',
            'NPASAJEROS', 'COSTO', 'MONEDA', 'COMENTARIOS', 'CONFIRMAR',
            'AGENTE_ID', 'AGENTE_NOMBRE', 'HOTEL_DESTINO', 'HOTEL_EXTRA',
            'OBSERVACIONES', 'PAGO', 'ACTIVO', 'SERVICIO_EXTRA', 'PICKUP_AT_HOTEL'
        ];

        // Comparar estructuras
        const devFields = devStructure.map(col => col.Field);

        const comparison = {
            tableName,
            expected: {
                columns: expectedColumns,
                count: expectedColumns.length
            },
            development: {
                columns: devFields,
                count: devFields.length
            },
            differences: []
        };

        // Campos que faltan en desarrollo
        const missingInDev = expectedColumns.filter(field => !devFields.includes(field));
        if (missingInDev.length > 0) {
            comparison.differences.push({
                type: 'missing_in_development',
                fields: missingInDev
            });
        }

        // Campos extra en desarrollo
        const extraInDev = devFields.filter(field => !expectedColumns.includes(field));
        if (extraInDev.length > 0) {
            comparison.differences.push({
                type: 'extra_in_development',
                fields: extraInDev
            });
        }

        res.json({
            success: true,
            data: comparison
        });

    } catch (error) {
        console.error('Error comparing table structures:', error);
        res.status(500).json({
            success: false,
            message: 'Error al comparar estructuras de tablas',
            error: error.message
        });
    }
};

// Función para obtener información sobre los filtros configurados
exports.getFiltersInfo = async (req, res) => {
    if (process.env.ENVIRONMENT !== 'DEVELOPMENT') {
        return res.status(403).json({
            success: false,
            error: 'Información de filtros solo disponible en desarrollo.'
        });
    }

    try {
        const filtersInfo = {};

        for (const table of TABLES) {
            const filter = TABLE_FILTERS[table];

            if (filter) {
                const filterInfo = {
                    hasFilter: true,
                    query: generateSelectQuery(table)
                };

                // Si tiene límite de registros
                if (filter.limitRecords) {
                    filterInfo.limitRecords = filter.limitRecords;
                    filterInfo.type = 'limit';

                    // Si tiene columna de orden específica (ej: ID_RESERVATION)
                    if (filter.orderColumn) {
                        filterInfo.orderColumn = filter.orderColumn;
                        filterInfo.orderDirection = filter.orderDirection || 'DESC';
                        filterInfo.orderBy = `${filter.orderColumn} ${filter.orderDirection || 'DESC'}`;
                    } else if (filter.dateColumn) {
                        filterInfo.dateColumn = filter.dateColumn;
                        filterInfo.orderBy = `${filter.dateColumn} DESC`;
                    }
                }

                // Si tiene filtro por años (lógica anterior)
                if (filter.yearsBack && filter.dateColumn) {
                    const currentDate = new Date();
                    const limitDate = new Date();
                    limitDate.setFullYear(currentDate.getFullYear() - filter.yearsBack);

                    const formattedLimitDate = `${limitDate.getDate().toString().padStart(2, '0')}/${(limitDate.getMonth() + 1).toString().padStart(2, '0')}/${limitDate.getFullYear()}`;
                    const formattedCurrentDate = `${currentDate.getDate().toString().padStart(2, '0')}/${(currentDate.getMonth() + 1).toString().padStart(2, '0')}/${currentDate.getFullYear()}`;

                    filterInfo.type = 'date_range';
                    filterInfo.dateColumn = filter.dateColumn;
                    filterInfo.yearsBack = filter.yearsBack;
                    filterInfo.dateFormat = filter.dateFormat;
                    filterInfo.dateRange = {
                        from: formattedLimitDate,
                        to: formattedCurrentDate
                    };
                }

                if (filter.dateFormat) {
                    filterInfo.dateFormat = filter.dateFormat;
                }

                filtersInfo[table] = filterInfo;
            } else {
                filtersInfo[table] = {
                    hasFilter: false,
                    query: generateSelectQuery(table)
                };
            }
        }

        res.json({
            success: true,
            data: {
                filters: filtersInfo,
                configured: TABLE_FILTERS
            }
        });

    } catch (err) {
        console.error('Error obteniendo información de filtros:', err);
        res.status(500).json({
            success: false,
            error: 'Error al obtener información de filtros.',
            details: err.message
        });
    }
};
