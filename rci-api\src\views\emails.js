let moment = require("moment");

exports.getTransroute = (reservation) => {
    let { destination, trip_type } = reservation.search_params;
    
    let label_type = "Arrival Information";
    
    if (destination === "AIRPORT SJD" && trip_type === "One Way") {
        label_type = "Departure Information";
    }
    
    console.log(reservation);

    return `${complementsHead()}
            <body style="-moz-box-sizing:border-box;-ms-text-size-adjust:100%;-webkit-box-sizing:border-box;-webkit-text-size-adjust:100%;Margin:0;background:#f5f5f5!important;box-sizing:border-box;color:#0a0a0a;font-family:Lato,sans-serif!important;font-size:16px;font-weight:400;line-height:1.3;margin:0;margin-top:.7rem;min-width:100%;padding:0;text-align:left;width:100%!important"><span class="preheader" style="color:#f5f5f5;display:none!important;font-size:1px;line-height:1px;max-height:0;max-width:0;mso-hide:all!important;opacity:0;overflow:hidden;visibility:hidden"></span>
            <table class="body" style="Margin:0;background:#f5f5f5!important;border-collapse:collapse;border-spacing:0;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:16px;font-weight:400;height:100%;line-height:1.3;margin:0;margin-top:.7rem;padding:0;text-align:left;vertical-align:top;width:100%">
                <tr style="padding:0;text-align:left;vertical-align:top">
                    <td class="center" align="center" valign="top" style="-moz-hyphens:auto;-webkit-hyphens:auto;Margin:0;border-collapse:collapse!important;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:16px;font-weight:400;hyphens:auto;line-height:1.3;margin:0;padding:0;text-align:left;vertical-align:top;word-wrap:break-word">
                        <center data-parsed style="min-width:580px;width:100%">
                            <table align="center" class="container float-center" style="Margin:0 auto;background:#fefefe;border-collapse:collapse;border-spacing:0;float:none;margin:0 auto;padding:0;text-align:center;vertical-align:top;width:580px">
                                <tbody>
                                    <tr style="padding:0;text-align:left;vertical-align:top">
                                        <td style="-moz-hyphens:auto;-webkit-hyphens:auto;Margin:0;border-collapse:collapse!important;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:16px;font-weight:400;hyphens:auto;line-height:1.3;margin:0;padding:0;text-align:left;vertical-align:top;word-wrap:break-word">
                                            <!--Header-->
                                            ${complementsHeader()}
                                            <!--End Header-->
                                            <table class="row" style="border-collapse:collapse;border-spacing:0;display:table;padding:0;position:relative;text-align:left;vertical-align:top;width:100%">
                                                <tbody>
                                                    <tr style="padding:0;text-align:left;vertical-align:top">
                                                        <th class="small-12 large-12 columns first last" style="Margin:0 auto;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:16px;font-weight:400;line-height:1.3;margin:0 auto;padding:0;padding-bottom:0;padding-left:16px;padding-right:16px;text-align:left;width:564px">
                                                            <table style="border-collapse:collapse;border-spacing:0;padding:0;text-align:left;vertical-align:top;width:100%">
                                                                <tr style="padding:0;text-align:left;vertical-align:top">
                                                                    <th style="Margin:0;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:16px;font-weight:400;line-height:1.3;margin:0;padding:0;text-align:left">
                                                                        <h1 style="Margin:0;Margin-bottom:5px;color:#3f729b;font-family:'Roboto Condensed',Arial,Helvetica,sans-serif;font-size:32px;font-weight:700;line-height:1.3;margin:2rem 0;margin-bottom:5px;padding:0;text-align:left;word-wrap:normal">THANK YOU FOR YOUR RESERVATION!</h1>
                                                                        <h4 style="Margin:0;Margin-bottom:5px;color:#b41900;font-family:'Roboto Condensed',Arial,Helvetica,sans-serif;font-size:23px;font-weight:700;line-height:1.3;margin:0;margin-bottom:5px;padding:0;text-align:left;word-wrap:normal">RCI-TRANSROUTE</h4>
                                                                        <h5 style="Margin:0;Margin-bottom:5px;color:#3f729b;font-family:'Roboto Condensed',Arial,Helvetica,sans-serif;font-size:20px;font-weight:400;line-height:1.3;margin:0;margin-bottom:5px;padding:0;text-align:left;word-wrap:normal"><b>CONFIRMATION SERVICE:</b> <span class="black-text" style="color:#000">${
                                                                            reservation.confirmation_service
                                                                        }</span></h5>
                                                                        <h5 style="Margin:0;Margin-bottom:5px;color:#3f729b;font-family:'Roboto Condensed',Arial,Helvetica,sans-serif;font-size:20px;font-weight:400;line-height:1.3;margin:0;margin-bottom:5px;padding:0;text-align:left;word-wrap:normal"><b>AUTHORIZATION CODE:</b> <span class="black-text" style="color:#000">${
                                                                            reservation.authorization_code
                                                                        }</span></h5>
                                                                        <table class="spacer" style="border-collapse:collapse;border-spacing:0;padding:0;text-align:left;vertical-align:top;width:100%">
                                                                            <tbody>
                                                                                <tr style="padding:0;text-align:left;vertical-align:top">
                                                                                    <td height="32px" style="-moz-hyphens:auto;-webkit-hyphens:auto;Margin:0;border-collapse:collapse!important;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:32px;font-weight:400;hyphens:auto;line-height:32px;margin:0;mso-line-height-rule:exactly;padding:0;text-align:left;vertical-align:top;word-wrap:break-word">&#xA0;</td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                        <table class="callout" style="Margin-bottom:16px;border-collapse:collapse;border-spacing:0;margin-bottom:0;padding:0;text-align:left;vertical-align:top;width:100%">
                                                                            <tr style="padding:0;text-align:left;vertical-align:top">
                                                                                <th class="callout-inner secondary" style="Margin:0;background:#f5f5f5!important;border:none;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:16px;font-weight:400;line-height:1.3;margin:0;padding:10px;text-align:left;width:100%">
                                                                                    <table class="row" style="border-collapse:collapse;border-spacing:0;display:table;padding:0;position:relative;text-align:left;vertical-align:top;width:100%">
                                                                                        <tbody>
                                                                                            <tr style="padding:0;text-align:left;vertical-align:top">
                                                                                                <th class="small-12 large-6 columns first" style="Margin:0 auto;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:16px;font-weight:400;line-height:1.3;margin:0 auto;padding:0;padding-bottom:0;padding-left:0!important;padding-right:0!important;text-align:left;width:50%">
                                                                                                    <table style="border-collapse:collapse;border-spacing:0;padding:0;text-align:left;vertical-align:top;width:100%">
                                                                                                        <tr style="padding:0;text-align:left;vertical-align:top">
                                                                                                            <th style="Margin:0;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:16px;font-weight:400;line-height:1.3;margin:0;padding:0;text-align:left">
                                                                                                                <h5 style="Margin:0;Margin-bottom:5px;color:#3f729b;font-family:'Roboto Condensed',Arial,Helvetica,sans-serif;font-size:20px;font-weight:400;line-height:1.3;margin:0;margin-bottom:5px;padding:0;text-align:left;word-wrap:normal">Pickup Location:</h5>
                                                                                                                <p style="Margin:0;Margin-bottom:16px;color:#4c4c4c;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:.9rem;font-weight:400;line-height:1.3;margin:0;margin-bottom:16px;padding:0;text-align:left">${
                                                                                                                    reservation
                                                                                                                        .search_params
                                                                                                                        .pickup_location
                                                                                                                }</p>
                                                                                                                <h5 style="Margin:0;Margin-bottom:5px;color:#3f729b;font-family:'Roboto Condensed',Arial,Helvetica,sans-serif;font-size:20px;font-weight:400;line-height:1.3;margin:0;margin-bottom:5px;padding:0;text-align:left;word-wrap:normal">Destination</h5>
                                                                                                                <p style="Margin:0;Margin-bottom:16px;color:#4c4c4c;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:.9rem;font-weight:400;line-height:1.3;margin:0;margin-bottom:16px;padding:0;text-align:left">${
                                                                                                                    reservation
                                                                                                                        .search_params
                                                                                                                        .destination
                                                                                                                }</p>
                                                                                                                <h5 style="Margin:0;Margin-bottom:5px;color:#3f729b;font-family:'Roboto Condensed',Arial,Helvetica,sans-serif;font-size:20px;font-weight:400;line-height:1.3;margin:0;margin-bottom:5px;padding:0;text-align:left;word-wrap:normal">Passenger Num.</h5>
                                                                                                                <p style="Margin:0;Margin-bottom:16px;color:#4c4c4c;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:.9rem;font-weight:400;line-height:1.3;margin:0;margin-bottom:16px;padding:0;text-align:left">${
                                                                                                                    reservation
                                                                                                                        .search_params
                                                                                                                        .n_passengers
                                                                                                                }</p>
                                                                                                            </th>
                                                                                                        </tr>
                                                                                                    </table>
                                                                                                </th>
                                                                                                <th class="small-12 large-6 columns last" style="Margin:0 auto;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:16px;font-weight:400;line-height:1.3;margin:0 auto;padding:0;padding-bottom:0;padding-left:0!important;padding-right:0!important;text-align:left;width:50%">
                                                                                                    <table style="border-collapse:collapse;border-spacing:0;padding:0;text-align:left;vertical-align:top;width:100%">
                                                                                                        <tr style="padding:0;text-align:left;vertical-align:top">
                                                                                                            <th style="Margin:0;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:16px;font-weight:400;line-height:1.3;margin:0;padding:0;text-align:left">
                                                                                                                <h5 style="Margin:0;Margin-bottom:5px;color:#3f729b;font-family:'Roboto Condensed',Arial,Helvetica,sans-serif;font-size:20px;font-weight:400;line-height:1.3;margin:0;margin-bottom:5px;padding:0;text-align:left;word-wrap:normal">Type Trip:</h5>
                                                                                                                <p style="Margin:0;Margin-bottom:16px;color:#4c4c4c;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:.9rem;font-weight:400;line-height:1.3;margin:0;margin-bottom:16px;padding:0;text-align:left">${
                                                                                                                    reservation
                                                                                                                        .search_params
                                                                                                                        .trip_type
                                                                                                                }</p>
                                                                                                                <h5 style="Margin:0;Margin-bottom:5px;color:#3f729b;font-family:'Roboto Condensed',Arial,Helvetica,sans-serif;font-size:20px;font-weight:400;line-height:1.3;margin:0;margin-bottom:5px;padding:0;text-align:left;word-wrap:normal">Price <b>(-10%)</b></h5>
                                                                                                                <p style="Margin:0;Margin-bottom:16px;color:#4c4c4c;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:.9rem;font-weight:400;line-height:1.3;margin:0;margin-bottom:16px;padding:0;text-align:left">$ ${
                                                                                                                    reservation
                                                                                                                        .search_params
                                                                                                                        .price
                                                                                                                } USD</p>
                                                                                                                <h5 style="Margin:0;Margin-bottom:5px;color:#3f729b;font-family:'Roboto Condensed',Arial,Helvetica,sans-serif;font-size:20px;font-weight:400;line-height:1.3;margin:0;margin-bottom:5px;padding:0;text-align:left;word-wrap:normal">Vehicle - Transport</h5>
                                                                                                                <p style="Margin:0;Margin-bottom:16px;color:#4c4c4c;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:.9rem;font-weight:400;line-height:1.3;margin:0;margin-bottom:16px;padding:0;text-align:left">${
                                                                                                                    reservation
                                                                                                                        .search_params
                                                                                                                        .transportation
                                                                                                                }</p>
                                                                                                            </th>
                                                                                                        </tr>
                                                                                                    </table>
                                                                                                </th>
                                                                                            </tr>
                                                                                        </tbody>
                                                                                    </table>
                                                                                </th>
                                                                                <th class="expander" style="Margin:0;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:16px;font-weight:400;line-height:1.3;margin:0;padding:0!important;text-align:left;visibility:hidden;width:0"></th>
                                                                            </tr>
                                                                        </table>
                                                                        <table class="spacer" style="border-collapse:collapse;border-spacing:0;padding:0;text-align:left;vertical-align:top;width:100%">
                                                                            <tbody>
                                                                                <tr style="padding:0;text-align:left;vertical-align:top">
                                                                                    <td height="10px" style="-moz-hyphens:auto;-webkit-hyphens:auto;Margin:0;border-collapse:collapse!important;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:10px;font-weight:400;hyphens:auto;line-height:10px;margin:0;mso-line-height-rule:exactly;padding:0;text-align:left;vertical-align:top;word-wrap:break-word">&#xA0;</td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                        <table class="row" style="border-collapse:collapse;border-spacing:0;display:table;padding:0;position:relative;text-align:left;vertical-align:top;width:100%">
                                                                            <tbody>
                                                                                <tr style="padding:0;text-align:left;vertical-align:top">
                                                                                    <th class="module small-12 large-6 columns first" style="Margin:0 auto;background:#f5f5f5;border-bottom:10px solid #fff;border-right:10px solid #fff;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:16px;font-weight:400;line-height:1.3;margin:0 auto;padding:0;padding-bottom:0;padding-left:0!important;padding-right:0!important;text-align:left;width:50%">
                                                                                        <table style="border-collapse:collapse;border-spacing:0;padding:0;text-align:left;vertical-align:top;width:100%">
                                                                                            <tr style="padding:0;text-align:left;vertical-align:top">
                                                                                                <th style="Margin:0;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:16px;font-weight:400;line-height:1.3;margin:0;padding:0;text-align:left">
                                                                                                    <table class="callout" style="Margin-bottom:16px;border-collapse:collapse;border-spacing:0;margin-bottom:0;padding:0;text-align:left;vertical-align:top;width:100%">
                                                                                                        <tr style="padding:0;text-align:left;vertical-align:top">
                                                                                                            <th class="callout-inner secondary" style="Margin:0;background:#f5f5f5!important;border:none;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:16px;font-weight:400;line-height:1.3;margin:0;padding:10px;text-align:left;width:100%">
                                                                                                                <h4 style="Margin:0;Margin-bottom:5px;color:#b41900;font-family:'Roboto Condensed',Arial,Helvetica,sans-serif;font-size:23px;font-weight:700;line-height:1.3;margin:0;margin-bottom:5px;padding:0;text-align:left;word-wrap:normal">${label_type}</h4>
                                                                                                                <h5 style="Margin:0;Margin-bottom:5px;color:#3f729b;font-family:'Roboto Condensed',Arial,Helvetica,sans-serif;font-size:20px;font-weight:400;line-height:1.3;margin:0;margin-bottom:5px;padding:0;text-align:left;word-wrap:normal">Date Time:</h5>
                                                                                                                <p style="Margin:0;Margin-bottom:16px;color:#4c4c4c;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:.9rem;font-weight:400;line-height:1.3;margin:0;margin-bottom:16px;padding:0;text-align:left">${
                                                                                                                    reservation
                                                                                                                        .arrival
                                                                                                                        .date_time
                                                                                                                }</p>
                                                                                                                <h5 style="Margin:0;Margin-bottom:5px;color:#3f729b;font-family:'Roboto Condensed',Arial,Helvetica,sans-serif;font-size:20px;font-weight:400;line-height:1.3;margin:0;margin-bottom:5px;padding:0;text-align:left;word-wrap:normal">Airline:</h5>
                                                                                                                <p style="Margin:0;Margin-bottom:16px;color:#4c4c4c;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:.9rem;font-weight:400;line-height:1.3;margin:0;margin-bottom:16px;padding:0;text-align:left">${
                                                                                                                    reservation
                                                                                                                        .arrival
                                                                                                                        .airline
                                                                                                                }</p>
                                                                                                                <h5 style="Margin:0;Margin-bottom:5px;color:#3f729b;font-family:'Roboto Condensed',Arial,Helvetica,sans-serif;font-size:20px;font-weight:400;line-height:1.3;margin:0;margin-bottom:5px;padding:0;text-align:left;word-wrap:normal">Flight number:</h5>
                                                                                                                <p style="Margin:0;Margin-bottom:16px;color:#4c4c4c;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:.9rem;font-weight:400;line-height:1.3;margin:0;margin-bottom:16px;padding:0;text-align:left">${
                                                                                                                    reservation
                                                                                                                        .arrival
                                                                                                                        .fligth_number
                                                                                                                }</p>
                                                                                                            </th>
                                                                                                            <th class="expander" style="Margin:0;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:16px;font-weight:400;line-height:1.3;margin:0;padding:0!important;text-align:left;visibility:hidden;width:0"></th>
                                                                                                        </tr>
                                                                                                    </table>
                                                                                                </th>
                                                                                            </tr>
                                                                                        </table>
                                                                                    </th>
                                                                                    <!--Departure Apartado-->
                                                                                    ${departure(reservation.departure)}
                                                                                    <!--Fin Departure-->
                                                                                    
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                        <table class="row" style="border-collapse:collapse;border-spacing:0;display:table;padding:0;position:relative;text-align:left;vertical-align:top;width:100%">
                                                                            <tbody>
                                                                                <tr style="padding:0;text-align:left;vertical-align:top">
                                                                                    <th class="module small-12 large-6 columns first" style="Margin:0 auto;background:#f5f5f5;border-bottom:10px solid #fff;border-right:10px solid #fff;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:16px;font-weight:400;line-height:1.3;margin:0 auto;padding:0;padding-bottom:0;padding-left:0!important;padding-right:0!important;text-align:left;width:50%">
                                                                                        <table style="border-collapse:collapse;border-spacing:0;padding:0;text-align:left;vertical-align:top;width:100%">
                                                                                            <tr style="padding:0;text-align:left;vertical-align:top">
                                                                                                <th style="Margin:0;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:16px;font-weight:400;line-height:1.3;margin:0;padding:0;text-align:left">
                                                                                                    <table class="callout" style="Margin-bottom:16px;border-collapse:collapse;border-spacing:0;margin-bottom:0;padding:0;text-align:left;vertical-align:top;width:100%">
                                                                                                        <tr style="padding:0;text-align:left;vertical-align:top">
                                                                                                            <th class="callout-inner secondary" style="Margin:0;background:#f5f5f5!important;border:none;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:16px;font-weight:400;line-height:1.3;margin:0;padding:10px;text-align:left;width:100%">
                                                                                                                <h4 style="Margin:0;Margin-bottom:5px;color:#b41900;font-family:'Roboto Condensed',Arial,Helvetica,sans-serif;font-size:23px;font-weight:700;line-height:1.3;margin:0;margin-bottom:5px;padding:0;text-align:left;word-wrap:normal">Customer Information</h4>
                                                                                                                <h5 style="Margin:0;Margin-bottom:5px;color:#3f729b;font-family:'Roboto Condensed',Arial,Helvetica,sans-serif;font-size:20px;font-weight:400;line-height:1.3;margin:0;margin-bottom:5px;padding:0;text-align:left;word-wrap:normal">Name:</h5>
                                                                                                                <p style="Margin:0;Margin-bottom:16px;color:#4c4c4c;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:.9rem;font-weight:400;line-height:1.3;margin:0;margin-bottom:16px;padding:0;text-align:left">${
                                                                                                                    reservation
                                                                                                                        .customer
                                                                                                                        .fullname
                                                                                                                }</p>
                                                                                                                <h5 style="Margin:0;Margin-bottom:5px;color:#3f729b;font-family:'Roboto Condensed',Arial,Helvetica,sans-serif;font-size:20px;font-weight:400;line-height:1.3;margin:0;margin-bottom:5px;padding:0;text-align:left;word-wrap:normal">Email:</h5>
                                                                                                                <p style="Margin:0;Margin-bottom:16px;color:#4c4c4c;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:.9rem;font-weight:400;line-height:1.3;margin:0;margin-bottom:16px;padding:0;text-align:left">${
                                                                                                                    reservation
                                                                                                                        .customer
                                                                                                                        .email
                                                                                                                }</p>
                                                                                                                <h5 style="Margin:0;Margin-bottom:5px;color:#3f729b;font-family:'Roboto Condensed',Arial,Helvetica,sans-serif;font-size:20px;font-weight:400;line-height:1.3;margin:0;margin-bottom:5px;padding:0;text-align:left;word-wrap:normal">Phone:</h5>
                                                                                                                <p style="Margin:0;Margin-bottom:16px;color:#4c4c4c;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:.9rem;font-weight:400;line-height:1.3;margin:0;margin-bottom:16px;padding:0;text-align:left">${
                                                                                                                    reservation
                                                                                                                        .customer
                                                                                                                        .phone
                                                                                                                }</p>
                                                                                                            </th>
                                                                                                            <th class="expander" style="Margin:0;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:16px;font-weight:400;line-height:1.3;margin:0;padding:0!important;text-align:left;visibility:hidden;width:0"></th>
                                                                                                        </tr>
                                                                                                    </table>
                                                                                                </th>
                                                                                            </tr>
                                                                                        </table>
                                                                                    </th>
                                                                                    <th class="module small-12 large-6 columns last" style="Margin:0 auto;background:#f5f5f5;border-bottom:10px solid #fff;border-left:10px solid #fff;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:16px;font-weight:400;line-height:1.3;margin:0 auto;padding:0;padding-bottom:0;padding-left:0!important;padding-right:0!important;text-align:left;width:50%">
                                                                                        <table style="border-collapse:collapse;border-spacing:0;padding:0;text-align:left;vertical-align:top;width:100%">
                                                                                            <tr style="padding:0;text-align:left;vertical-align:top">
                                                                                                <th style="Margin:0;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:16px;font-weight:400;line-height:1.3;margin:0;padding:0;text-align:left">
                                                                                                    <table class="callout" style="Margin-bottom:16px;border-collapse:collapse;border-spacing:0;margin-bottom:0;padding:0;text-align:left;vertical-align:top;width:100%">
                                                                                                        <tr style="padding:0;text-align:left;vertical-align:top">
                                                                                                            <th class="callout-inner secondary" style="Margin:0;background:#f5f5f5!important;border:none;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:16px;font-weight:400;line-height:1.3;margin:0;padding:10px;text-align:left;width:100%">
                                                                                                                <h4 style="Margin:0;Margin-bottom:5px;color:#b41900;font-family:'Roboto Condensed',Arial,Helvetica,sans-serif;font-size:23px;font-weight:700;line-height:1.3;margin:0;margin-bottom:5px;padding:0;text-align:left;word-wrap:normal">Agent's information</h4>
                                                                                                                <h5 style="Margin:0;Margin-bottom:5px;color:#3f729b;font-family:'Roboto Condensed',Arial,Helvetica,sans-serif;font-size:20px;font-weight:400;line-height:1.3;margin:0;margin-bottom:5px;padding:0;text-align:left;word-wrap:normal">ID Agent:</h5>
                                                                                                                <p style="Margin:0;Margin-bottom:16px;color:#4c4c4c;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:.9rem;font-weight:400;line-height:1.3;margin:0;margin-bottom:16px;padding:0;text-align:left">
                                                                                                                    ${reservation.agent.id}</p>
                                                                                                                <h5 style="Margin:0;Margin-bottom:5px;color:#3f729b;font-family:'Roboto Condensed',Arial,Helvetica,sans-serif;font-size:20px;font-weight:400;line-height:1.3;margin:0;margin-bottom:5px;padding:0;text-align:left;word-wrap:normal">ID Partner:</h5>
                                                                                                                <p style="Margin:0;Margin-bottom:16px;color:#4c4c4c;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:.9rem;font-weight:400;line-height:1.3;margin:0;margin-bottom:16px;padding:0;text-align:left">
                                                                                                                    ${reservation.agent.id_partner}</p>
                                                                                                            </th>
                                                                                                            <th class="expander" style="Margin:0;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:16px;font-weight:400;line-height:1.3;margin:0;padding:0!important;text-align:left;visibility:hidden;width:0"></th>
                                                                                                        </tr>
                                                                                                    </table>
                                                                                                </th>
                                                                                            </tr>
                                                                                        </table>
                                                                                    </th>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                            ${extraService(reservation.extra_service)}
                                                                        <table class="spacer" style="border-collapse:collapse;border-spacing:0;padding:0;text-align:left;vertical-align:top;width:100%">
                                                                            <tbody>
                                                                                <tr style="padding:0;text-align:left;vertical-align:top">
                                                                                    <td height="32px" style="-moz-hyphens:auto;-webkit-hyphens:auto;Margin:0;border-collapse:collapse!important;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:32px;font-weight:400;hyphens:auto;line-height:32px;margin:0;mso-line-height-rule:exactly;padding:0;text-align:left;vertical-align:top;word-wrap:break-word">&#xA0;</td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                        <h4 class="comments-title" style="Margin:0;Margin-bottom:5px;color:#b41900;font-family:'Roboto Condensed',Arial,Helvetica,sans-serif;font-size:23px;font-weight:700;line-height:1.3;margin:0;margin-bottom:5px;padding:0;text-align:left;word-wrap:normal">Comments</h4>
                                                                        <hr>
                                                                        <table class="row" style="border-collapse:collapse;border-spacing:0;display:table;padding:0;position:relative;text-align:left;vertical-align:top;width:100%">
                                                                            <tbody>
                                                                                <tr style="padding:0;text-align:left;vertical-align:top">
                                                                                    <p style="Margin:0;Margin-bottom:16px;color:#4c4c4c;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:.9rem;font-weight:400;line-height:1.3;margin:0;margin-bottom:16px;padding:0;text-align:left">${
                                                                                        reservation.search_params.comments
                                                                                    }</p>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                        <hr>
                                                                        <!---Complements--->
                                                                        ${complementsFooter()}
                                                                        <!---End Complements--->
                                                                    </th>
                                                                </tr>
                                                            </table>
                                                        </th>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </center>
                    </td>
                </tr>
            </table>
            <!-- prevent Gmail on iOS font size manipulation -->
            <div style="display:none;white-space:nowrap;font:15px courier;line-height:0">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</div>
        </body>
        </html>`;
};

const departure = (departure) => {
    var temp = ``;

    if (departure != null && departure != undefined && departure != "") {
        temp = `<th class="module small-12 large-6 columns last" style="Margin:0 auto;background:#f5f5f5;border-bottom:10px solid #fff;border-left:10px solid #fff;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:16px;font-weight:400;line-height:1.3;margin:0 auto;padding:0;padding-bottom:0;padding-left:0!important;padding-right:0!important;text-align:left;width:50%">
                <table style="border-collapse:collapse;border-spacing:0;padding:0;text-align:left;vertical-align:top;width:100%">
                    <tr style="padding:0;text-align:left;vertical-align:top">
                        <th style="Margin:0;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:16px;font-weight:400;line-height:1.3;margin:0;padding:0;text-align:left">
                            <table class="callout" style="Margin-bottom:16px;border-collapse:collapse;border-spacing:0;margin-bottom:0;padding:0;text-align:left;vertical-align:top;width:100%">
                                <tr style="padding:0;text-align:left;vertical-align:top">
                                    <th class="callout-inner secondary" style="Margin:0;background:#f5f5f5!important;border:none;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:16px;font-weight:400;line-height:1.3;margin:0;padding:10px;text-align:left;width:100%">
                                        <h4 style="Margin:0;Margin-bottom:5px;color:#b41900;font-family:'Roboto Condensed',Arial,Helvetica,sans-serif;font-size:23px;font-weight:700;line-height:1.3;margin:0;margin-bottom:5px;padding:0;text-align:left;word-wrap:normal">Departure Information</h4>
                                        <h5 style="Margin:0;Margin-bottom:5px;color:#3f729b;font-family:'Roboto Condensed',Arial,Helvetica,sans-serif;font-size:20px;font-weight:400;line-height:1.3;margin:0;margin-bottom:5px;padding:0;text-align:left;word-wrap:normal">Date Time:</h5>
                                        <p style="Margin:0;Margin-bottom:16px;color:#4c4c4c;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:.9rem;font-weight:400;line-height:1.3;margin:0;margin-bottom:16px;padding:0;text-align:left">${departure.date_time}</p>
                                        <h5 style="Margin:0;Margin-bottom:5px;color:#3f729b;font-family:'Roboto Condensed',Arial,Helvetica,sans-serif;font-size:20px;font-weight:400;line-height:1.3;margin:0;margin-bottom:5px;padding:0;text-align:left;word-wrap:normal">Hotel Pickup Time:</h5>
                                        <p style="Margin:0;Margin-bottom:16px;color:#4c4c4c;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:.9rem;font-weight:400;line-height:1.3;margin:0;margin-bottom:16px;padding:0;text-align:left">${departure.pickup_at_hotel}</p>
                                        <h5 style="Margin:0;Margin-bottom:5px;color:#3f729b;font-family:'Roboto Condensed',Arial,Helvetica,sans-serif;font-size:20px;font-weight:400;line-height:1.3;margin:0;margin-bottom:5px;padding:0;text-align:left;word-wrap:normal">Airline:</h5>
                                        <p style="Margin:0;Margin-bottom:16px;color:#4c4c4c;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:.9rem;font-weight:400;line-height:1.3;margin:0;margin-bottom:16px;padding:0;text-align:left">${departure.airline}</p>
                                        <h5 style="Margin:0;Margin-bottom:5px;color:#3f729b;font-family:'Roboto Condensed',Arial,Helvetica,sans-serif;font-size:20px;font-weight:400;line-height:1.3;margin:0;margin-bottom:5px;padding:0;text-align:left;word-wrap:normal">Flight number:</h5>
                                        <p style="Margin:0;Margin-bottom:16px;color:#4c4c4c;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:.9rem;font-weight:400;line-height:1.3;margin:0;margin-bottom:16px;padding:0;text-align:left">${departure.fligth_number}</p>
                                        <h5 style="Margin:0;Margin-bottom:5px;color:#3f729b;font-family:'Roboto Condensed',Arial,Helvetica,sans-serif;font-size:20px;font-weight:400;line-height:1.3;margin:0;margin-bottom:5px;padding:0;text-align:left;word-wrap:normal">Departure Hotel Pickup:</h5>
                                        <p style="Margin:0;Margin-bottom:16px;color:#4c4c4c;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:.9rem;font-weight:400;line-height:1.3;margin:0;margin-bottom:16px;padding:0;text-align:left">${departure.hotel_pickup}</p>
                                    </th>
                                    <th class="expander" style="Margin:0;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:16px;font-weight:400;line-height:1.3;margin:0;padding:0!important;text-align:left;visibility:hidden;width:0"></th>
                                </tr>
                            </table>
                        </th>
                    </tr>
                </table>
            </th>`;
    }

    return temp;
};
const extraService = (service) =>{
    let resp;
    if (service != null && service != undefined && service != "") {
        resp = `<table class="spacer" style="border-collapse:collapse;border-spacing:0;padding:0;text-align:left;vertical-align:top;width:100%">
            <tbody>
                <tr style="padding:0;text-align:left;vertical-align:top">
                    <td height="32px" style="-moz-hyphens:auto;-webkit-hyphens:auto;Margin:0;border-collapse:collapse!important;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:32px;font-weight:400;hyphens:auto;line-height:32px;margin:0;mso-line-height-rule:exactly;padding:0;text-align:left;vertical-align:top;word-wrap:break-word">&#xA0;</td>
                </tr>
            </tbody>
        </table>
        <h4 class="comments-title" style="Margin:0;Margin-bottom:5px;color:#b41900;font-family:'Roboto Condensed',Arial,Helvetica,sans-serif;font-size:23px;font-weight:700;line-height:1.3;margin:0;margin-bottom:5px;padding:0;text-align:left;word-wrap:normal">Extra Service</h4>
        <hr>
        <table class="row" style="border-collapse:collapse;border-spacing:0;display:table;padding:0;position:relative;text-align:left;vertical-align:top;width:100%">
            <tbody>
                <tr style="padding:0;text-align:left;vertical-align:top">
                    <h5 style="Margin:0;Margin-bottom:5px;color:#3f729b;font-family:'Roboto Condensed',Arial,Helvetica,sans-serif;font-size:20px;font-weight:400;line-height:1.3;margin:0;margin-bottom:5px;padding:0;text-align:left;word-wrap:normal">
                        Service Name:
                    </h5>
                    <p style="Margin:0;Margin-bottom:16px;color:#4c4c4c;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:.9rem;font-weight:400;line-height:1.3;margin:0;margin-bottom:16px;padding:0;text-align:left">
                       ${service.name}</p>
                    <h5 style="Margin:0;Margin-bottom:5px;color:#3f729b;font-family:'Roboto Condensed',Arial,Helvetica,sans-serif;font-size:20px;font-weight:400;line-height:1.3;margin:0;margin-bottom:5px;padding:0;text-align:left;word-wrap:normal">
                        Service Price:
                    </h5>
                    <p style="Margin:0;Margin-bottom:16px;color:#4c4c4c;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:.9rem;font-weight:400;line-height:1.3;margin:0;margin-bottom:16px;padding:0;text-align:left">
                        $${service.price} USD</p>
                    <h5 style="Margin:0;Margin-bottom:5px;color:#3f729b;font-family:'Roboto Condensed',Arial,Helvetica,sans-serif;font-size:20px;font-weight:400;line-height:1.3;margin:0;margin-bottom:5px;padding:0;text-align:left;word-wrap:normal">
                        Service Time Limited:
                    </h5>
                    <p style="Margin:0;Margin-bottom:16px;color:#4c4c4c;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:.9rem;font-weight:400;line-height:1.3;margin:0;margin-bottom:16px;padding:0;text-align:left">
                        ${service.time}</p>
                </tr>
            </tbody>
        </table>
        <hr>`
    }else {
        resp = ''
    }
    return resp;
}

const complementsHeader = () => {
    return `<table class="spacer" style="border-collapse:collapse;border-spacing:0;padding:0;text-align:left;vertical-align:top;width:100%">
                <tbody>
                    <tr style="padding:0;text-align:left;vertical-align:top">
                        <td height="16px" style="-moz-hyphens:auto;-webkit-hyphens:auto;Margin:0;border-collapse:collapse!important;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:16px;font-weight:400;hyphens:auto;line-height:16px;margin:0;mso-line-height-rule:exactly;padding:0;text-align:left;vertical-align:top;word-wrap:break-word">&#xA0;</td>
                    </tr>
                </tbody>
            </table>
            <table class="row" style="border-collapse:collapse;border-spacing:0;display:table;padding:0;position:relative;text-align:left;vertical-align:top;width:100%">
                <tbody>
                    <tr style="padding:0;text-align:left;vertical-align:top">
                        <th class="small-12 large-6 columns first last" style="Margin:0 auto;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:16px;font-weight:400;line-height:1.3;margin:0 auto;padding:0;padding-bottom:0;padding-left:16px;padding-right:16px;text-align:left;width:274px">
                            <table style="border-collapse:collapse;border-spacing:0;padding:0;text-align:left;vertical-align:top;width:100%">
                                <tr style="padding:0;text-align:left;vertical-align:top">
                                    <th style="Margin:0;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:16px;font-weight:400;line-height:1.3;margin:0;padding:0;text-align:left"><img class="float-center" src="https://rci.transroute.com.mx/header-mail.png" alt="RCI - Transroute" title="RCI - Transroute" style="-ms-interpolation-mode:bicubic;Margin:0 auto;clear:both;display:block;float:none;margin:0 auto;max-width:100%;outline:0;text-align:center;text-decoration:none;width:auto"></th>
                                </tr>
                            </table>
                        </th>
                    </tr>
                </tbody>
            </table>`;
};

const complementsFooter = () => {
    return `<table class="spacer" style="border-collapse:collapse;border-spacing:0;padding:0;text-align:left;vertical-align:top;width:100%">
                <tbody>
                    <tr style="padding:0;text-align:left;vertical-align:top">
                        <td height="24px" style="-moz-hyphens:auto;-webkit-hyphens:auto;Margin:0;border-collapse:collapse!important;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:24px;font-weight:400;hyphens:auto;line-height:24px;margin:0;mso-line-height-rule:exactly;padding:0;text-align:left;vertical-align:top;word-wrap:break-word">&#xA0;</td>
                    </tr>
                    <tr>
                        <td>
                            <a href="/benefits" target="_blank"><img src="https://booking.transroute.com.mx/benefits-email.png" width="100%" /></a>
                        </td>
                    </tr>
                </tbody>
            </table>
            <table class="row" style="border-collapse:collapse;border-spacing:0;display:table;padding:0;position:relative;text-align:left;vertical-align:top;width:100%">
                <tbody>
                    <tr style="padding:0;text-align:left;vertical-align:top">
                        <h5 style="Margin:0;Margin-bottom:5px;color:#3f729b;font-family:'Roboto Condensed',Arial,Helvetica,sans-serif;font-size:20px;font-weight:400;line-height:1.3;margin:0;margin-bottom:5px;padding:0;text-align:left;word-wrap:normal">Maps</h5>
                        <p style="Margin:0;Margin-bottom:16px;color:#4c4c4c;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:.9rem;font-weight:400;line-height:1.3;margin:0;margin-bottom:16px;padding:0;text-align:left">Important information for arrival at the airport, please follow the arrows in the image:</p>
                        <table class="callout" style="Margin-bottom:16px;border-collapse:collapse;border-spacing:0;margin-bottom:0;padding:0;text-align:left;vertical-align:top;width:100%">
                            <tr style="padding:0;text-align:left;vertical-align:top">
                                <th class="callout-inner secondary" style="Margin:0;background:#f5f5f5!important;border:none;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:16px;font-weight:400;line-height:1.3;margin:0;padding:10px;text-align:left;width:100%">
                                    <p style="Margin:0;Margin-bottom:16px;color:#4c4c4c;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:.9rem;font-weight:400;line-height:1.3;margin:0;margin-bottom:16px;padding:0;text-align:left"><img class="float-center" src="https://rci.transroute.com.mx/terminal-1.png" alt="Los Cabos Airport Terminal #1 - Transroute" title="Los Cabos Airport Terminal #1 - Transroute" style="-ms-interpolation-mode:bicubic;Margin:0 auto;clear:both;display:block;float:none;margin:0 auto;max-width:100%;outline:0;text-align:center;text-decoration:none;width:auto"></p>
                                    <p style="Margin:0;Margin-bottom:16px;color:#4c4c4c;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:.9rem;font-weight:400;line-height:1.3;margin:0;margin-bottom:16px;padding:0;text-align:left"><img class="float-center" src="https://rci.transroute.com.mx/terminal-2.png" alt="Los Cabos Airport Terminal #2 - Transroute" title="Los Cabos Airport Terminal #2 - Transroute" style="-ms-interpolation-mode:bicubic;Margin:0 auto;clear:both;display:block;float:none;margin:0 auto;max-width:100%;outline:0;text-align:center;text-decoration:none;width:auto"></p>
                                </th>
                                <th class="expander" style="Margin:0;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:16px;font-weight:400;line-height:1.3;margin:0;padding:0!important;text-align:left;visibility:hidden;width:0"></th>
                            </tr>
                        </table>
                        <p style="Margin:0;Margin-bottom:16px;color:#4c4c4c;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:.9rem;font-weight:400;line-height:1.3;margin:0;margin-bottom:16px;padding:0;text-align:left">Terminal 1 for domestic arrivals, Terminal 2 for international arrivals.</p>
                    </tr>
                </tbody>
            </table>
            <hr>
			<table class="row" style="border-collapse:collapse;border-spacing:0;display:table;padding:0;position:relative;text-align:left;vertical-align:top;width:100%">
                <tbody>
                    <tr style="padding:0;text-align:left;vertical-align:top">
                        <h5 style="Margin:0;Margin-bottom:5px;color:#3f729b;font-family:'Roboto Condensed',Arial,Helvetica,sans-serif;font-size:20px;font-weight:400;line-height:1.3;margin:0;margin-bottom:5px;padding:0;text-align:left;word-wrap:normal">Airport Pickup</h5>
                        <p style="Margin:0;Margin-bottom:16px;color:#4c4c4c;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:.9rem;font-weight:400;line-height:1.3;margin:0;margin-bottom:16px;padding:0;text-align:left">
                            Outside of Terminal 2 go to the umbrella number 3, one of our representatives will assist you.</p>
                        <table class="callout" style="Margin-bottom:16px;border-collapse:collapse;border-spacing:0;margin-bottom:0;padding:0;text-align:left;vertical-align:top;width:100%">
                            <tr style="padding:0;text-align:left;vertical-align:top">
                                <th class="callout-inner secondary" style="Margin:0;background:#f5f5f5!important;border:none;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:16px;font-weight:400;line-height:1.3;margin:0;padding:10px;text-align:left;width:100%">
                                    <p style="Margin:0;Margin-bottom:16px;color:#4c4c4c;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:.9rem;font-weight:400;line-height:1.3;margin:0;margin-bottom:16px;padding:0;text-align:left"><img class="float-center" src="https://rci.transroute.com.mx/transroute-pickup.jpg" alt="Los Cabos Airport Terminal #1 - Transroute" title="Los Cabos Airport Terminal #1 - Transroute" style="-ms-interpolation-mode:bicubic;Margin:0 auto;clear:both;display:block;float:none;margin:0 auto;max-width:100%;outline:0;text-align:center;text-decoration:none;width:auto"></p>
                                </th>
                                <th class="expander" style="Margin:0;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:16px;font-weight:400;line-height:1.3;margin:0;padding:0!important;text-align:left;visibility:hidden;width:0"></th>
                            </tr>
                        </table>                       
                    </tr>
                </tbody>
            </table>
			<hr>
            <table class="spacer" style="border-collapse:collapse;border-spacing:0;padding:0;text-align:left;vertical-align:top;width:100%">
                <tbody>
                    <tr style="padding:0;text-align:left;vertical-align:top">
                        <td height="24px" style="-moz-hyphens:auto;-webkit-hyphens:auto;Margin:0;border-collapse:collapse!important;color:#0a0a0a;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:24px;font-weight:400;hyphens:auto;line-height:24px;margin:0;mso-line-height-rule:exactly;padding:0;text-align:left;vertical-align:top;word-wrap:break-word">&#xA0;</td>
                    </tr>
                </tbody>
            </table>
            <table class="row" style="border-collapse:collapse;border-spacing:0;display:table;padding:0;position:relative;text-align:left;vertical-align:top;width:100%">
                <tbody>
                    <tr style="padding:0;text-align:left;vertical-align:top">
                        <h5 style="Margin:0;Margin-bottom:5px;color:#3f729b;font-family:'Roboto Condensed',Arial,Helvetica,sans-serif;font-size:20px;font-weight:400;line-height:1.3;margin:0;margin-bottom:5px;padding:0;text-align:left;word-wrap:normal">RESERVATION / CANCELATION POLICY</h5>
                        <small style="color:#4c4c4c;font-size:80%">
                            <ul>
                                <li>All Reservations are non-transferable</li>
                                <li>In order to ensure availability of vehicles we ask that all reservations must be made at least 48hrs prior to transportation service date, being arrival or departure.</li><li>For arrival modifications a 48 hour notification must be made prior to schedule.</li>
                                <li>For departure modifications a 24 hour notification must be made prior to schedule.</li>
                                <li>Any cancelation including any major causes will be subject to a 10% retention fee for administrative costs.</li>
                                <li>All cancelations must be made at least 48hrs prior to service date, being arrival or departure</li>
                                <li>All cancelations made within the 48hrs time frame will be subject to 50% retention of the total cost.</li>
                                <li>Any no show on either arrival/departure date or on both dates will be subject to 100% retention of the total cost.</li>
                                <li>Partial or total refunds will be applied according on times, payment method and penalty percentage defined by the payment system chosen. **Claims made after the 30day period or any cancelation that does not meet cancelation criteria mentioned above will be completely non-refundable.</li>
                            </ul>
                        </small>
                    </tr>
                </tbody>
            </table>
            <hr>
            <table class="row" style="border-collapse:collapse;border-spacing:0;display:table;padding:0;position:relative;text-align:left;vertical-align:top;width:100%">
                <tbody>
                    <tr style="padding:0;text-align:left;vertical-align:top">
                        <p style="Margin:0;Margin-bottom:16px;color:#4c4c4c;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:.9rem;font-weight:400;line-height:1.3;margin:0;margin-bottom:16px;padding:0;text-align:left">RCI PHONE: 1 888 7246157 - TRANSROUTE PHONE: +52**************
                            <br>ATTENTION SCHEDULE RCI: MONDAY TO FRIDAY: 9:00 hrs. a las 17:00 hrs. SATURDAY: 9:00 hrs. a las 14:00 hrs.</p>
                        <p style="Margin:0;Margin-bottom:16px;color:#4c4c4c;font-family:Lato,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;font-size:.9rem;font-weight:400;line-height:1.3;margin:0;margin-bottom:16px;padding:0;text-align:left"><b>RCI and Transroute appreciates your preference.</b></p>
                    </tr>
                </tbody>
            </table>`;
};

const complementsHead = () => {
    return `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
            <html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en" style="background:#f5f5f5!important;margin-top:.7rem">
            
            <head>
                <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
                <meta name="viewport" content="width=device-width">
                <title>Order Template Subject</title>
                <style>
                    @media only screen {
                        html {
                            min-height: 100%;
                            background: #f5f5f5
                        }
                    }
                    
                    @media only screen and (max-width:596px) {
                        .small-float-center {
                            margin: 0 auto!important;
                            float: none!important;
                            text-align: center!important
                        }
                    }
                    
                    @media only screen and (max-width:596px) {
                        table.body table.container .show-for-large {
                            display: none!important;
                            width: 0;
                            mso-hide: all;
                            overflow: hidden
                        }
                    }
                    
                    @media only screen and (max-width:596px) {
                        table.body img {
                            width: auto;
                            height: auto
                        }
                        table.body center {
                            min-width: 0!important
                        }
                        table.body .container {
                            width: 95%!important
                        }
                        table.body .columns {
                            height: auto!important;
                            -moz-box-sizing: border-box;
                            -webkit-box-sizing: border-box;
                            box-sizing: border-box;
                            padding-left: 16px!important;
                            padding-right: 16px!important
                        }
                        table.body .columns .columns {
                            padding-left: 0!important;
                            padding-right: 0!important
                        }
                        table.body .collapse .columns {
                            padding-left: 0!important;
                            padding-right: 0!important
                        }
                        th.small-3 {
                            display: inline-block!important;
                            width: 25%!important
                        }
                        th.small-6 {
                            display: inline-block!important;
                            width: 50%!important
                        }
                        th.small-12 {
                            display: inline-block!important;
                            width: 100%!important
                        }
                        .columns th.small-12 {
                            display: block!important;
                            width: 100%!important
                        }
                        table.menu {
                            width: 100%!important
                        }
                        table.menu td,
                        table.menu th {
                            width: auto!important;
                            display: inline-block!important
                        }
                        table.menu.vertical td,
                        table.menu.vertical th {
                            display: block!important
                        }
                        table.menu[align=center] {
                            width: auto!important
                        }
                    }
                    
                    @media only screen and (max-width:736px) {
                        .module:nth-child(1) {
                            border-right: 0;
                            border-bottom: 10px solid #fff
                        }
                        .module:nth-child(2) {
                            border-left: 0;
                            border-bottom: 10px solid #fff
                        }
                        p {
                            font-size: inherit
                        }
                    }
                </style>
            </head>`;
};
