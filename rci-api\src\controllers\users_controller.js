const { pool } = require("../database");
const crypto = require("crypto");
const utils = require("./../utils");

const _TABLE = "USUARIOS";
const _ID = "ID_USUARIO";

exports.get = (req, res) => {
    const sql = `SELECT * FROM ${_TABLE} WHERE ACTIVO <> 3`;

    pool.query(sql, (error, result) => {
        if (error) {
            console.error("Error al obtener usuarios:", error);
            return res.status(500).json({ status: 500, results: "Error al obtener usuarios." });
        }

        const response = {
            status: 200,
            results: utils.userParser(result),
        };
        res.status(200).json(response);
    });
};

exports.getById = (req, res) => {
    const sql = `SELECT * FROM ${_TABLE} WHERE ACTIVO <> 3 AND ${_ID} = ?`;
    const params = [req.params.id];

    pool.query(sql, params, (error, result) => {
        if (error) {
            console.error("Error al obtener usuario por ID:", error);
            return res.status(500).json({ status: 500, results: "Error al obtener el usuario." });
        }

        const response = {
            status: 200,
            results: utils.userParser(result),
        };
        res.status(200).json(response);
    });
};

exports.insert = (req, res) => {
    req.body.CONTRA = crypto.createHash("md5").update(req.body.CONTRA).digest("hex");

    try {
        utils.functionValidate(req.body, (isValid) => {
            if (isValid) {
                const sql = `INSERT INTO ${_TABLE} SET ?`;

                pool.query(sql, req.body, (err, result) => {
                    if (err) {
                        console.error("Error al insertar usuario:", err);
                        return res.status(500).json({ status: 500, results: "Error al insertar el usuario." });
                    }

                    const response = {
                        status: 200,
                        results: result,
                    };
                    res.status(200).json(response);
                });
            } else {
                return res.status(405).json({ status: 405, results: "Missing Parameters" });
            }
        });
    } catch (error) {
        console.error("Error en el proceso de inserción:", error);
        return res.status(500).json({ status: 500, results: "Error en el servidor." });
    }
};

exports.update = (req, res) => {
    if (req.body.CONTRA) {
        req.body.CONTRA = crypto.createHash("md5").update(req.body.CONTRA).digest("hex");
    }

    try {
        utils.functionValidate(req.body, (isValid) => {
            if (isValid) {
                let sql = `UPDATE ${_TABLE} SET `;
                const data = [];

                // Construir la consulta SQL dinámicamente
                Object.keys(req.body).forEach((key, index) => {
                    sql += `${key} = ?${index < Object.keys(req.body).length - 1 ? ", " : " "}`;
                    data.push(req.body[key]);
                });

                sql += `WHERE ${_ID} = ?`;
                data.push(req.params.id);

                pool.query(sql, data, (err, result) => {
                    if (err) {
                        console.error("Error al actualizar usuario:", err);
                        return res.status(500).json({ status: 500, results: "Error al actualizar el usuario." });
                    }

                    const response = {
                        status: 200,
                        results: result,
                    };
                    res.status(200).json(response);
                });
            } else {
                return res.status(405).json({ status: 405, results: "Missing Parameters" });
            }
        });
    } catch (error) {
        console.error("Error en el proceso de actualización:", error);
        return res.status(500).json({ status: 500, results: "Error en el servidor." });
    }
};

exports.delete = (req, res) => {
    try {
        const sql = `UPDATE ${_TABLE} SET ACTIVO = ? WHERE ${_ID} = ?`;
        const data = [3, req.params.id];

        pool.query(sql, data, (err, result) => {
            if (err) {
                console.error("Error al eliminar usuario:", err);
                return res.status(500).json({ status: 500, results: "Error al eliminar el usuario." });
            }

            const response = {
                status: 200,
                results: { message: "Usuario eliminado exitosamente." },
            };
            res.status(200).json(response);
        });
    } catch (error) {
        console.error("Error en el proceso de eliminación:", error);
        return res.status(500).json({ status: 500, results: "Error en el servidor." });
    }
};