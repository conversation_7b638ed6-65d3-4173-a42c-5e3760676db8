const jwt = require("jsonwebtoken");
const _key = require("./../utils");

function verifyToken(req, res, next) {
    const token = req.headers.authorization;

    // Verificar si el token está presente
    if (!token) {
        return res.status(401).json({
            auth: false,
            message: "No token provided",
        });
    }

    // Verificar el token
    jwt.verify(token, _key.salat, (error, decoded) => {
        if (error) {
            return res.status(401).json({
                auth: false,
                message: "Invalid token",
            });
        }

        // Almacenar los datos decodificados en la solicitud
        req.userId = decoded.id;
        req.id_rol = decoded.id_rol;
        next();
    });
}

module.exports = verifyToken;