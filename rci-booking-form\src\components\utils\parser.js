import moment from "moment";

// *********
// Helpers
// *********

const getCodeAux = () => {
    return `TR${moment().year("YY").format("YY")}${moment().month("M").format("MM")}${moment()
        .day("D")
        .format("DD")}-${Math.floor(Math.random() * 99999)}`;
};

const parseRate = (rate) => {
    let RegularRate = rate * 100 / 90;
        RegularRate = Number((RegularRate).toFixed(1))
    let promotionRate = RegularRate - RegularRate * 0.1;
        promotionRate = Number((promotionRate).toFixed(1))
    // actualmente la tarifa con 20% de descuento
    return promotionRate;
};

// *********
// Parsers
// *********

export const parserEmail = (data, id) => {
    let email = {
        email_to: data.contact_email,
        option: "confirm-tr",
        data: {
            confirmation_service: id,
            authorization_code: data.payment_auth_code,
            search_params: {
                pickup_location: data.pickup,
                destination: data.destination,
                n_passengers: data.passengers,
                trip_type: data.trip_type === "Round Trip" ? "Round Trip" : "One Way",
                price: parseRate(data.rate),
                transportation: data.transport,
                comments: data.observations,
            },
            arrival: {
                date_time: `${
                    moment(data.arrival_datetime).format("MM/DD/YYYY") + " -- " + data.arrival_time
                }`,
                airline: data.arrival_airline,
                fligth_number: data.arrival_flight,
            },
            customer: {
                fullname: data.contact_name,
                email: data.contact_email,
                phone: data.contact_phone,
            },
            agent: {
                id: data.agent_id,
                id_partner: data.member_id,
            },           
        },
    };
    
    if (data.trip_type === "Round Trip") {
        email.data.departure = {
            date_time: `${
                moment(data.departure_datetime).format("MM/DD/YYYY") + " -- " + data.departure_time
            }`,
            airline: data.departure_airline,
            fligth_number: data.departure_flight,
            hotel_pickup: data.hotel_extra || data.destination,
            pickup_at_hotel: data.departure_pickup_time_hotel,
        };
    }
    if (data.isServiceActive) {
        email.data.extra_service = {
            name: data.extra_service_selected.name,
            price: data.extra_service_selected.price,
            time: data.extra_service_selected.time,
        }
    }
    
    return email;
};

export const parserEmailEdit = (data) => {
    if (data.trip_type === "One Way") {
        return {
            email_to: data.selected_res.CORREO,
            option: "confirm-tr",
            data: {
                confirmation_service: data.selected_res.ID,
                authorization_code: data.selected_res.PAGO,
                search_params: {
                    pickup_location: data.arrival_hotel,
                    destination: data.hotel_destination,
                    n_passengers: data.passengers,
                    trip_type: data.hasdeparture ? "Round Trip" : "One Way",
                    price: data.selected_res.COSTO,
                    transportation: data.transport,
                    comments: data.observations,
                },
                arrival: {
                    date_time: data.arrival_date + " " + data.arrival_time,
                    airline: data.arrival_airline,
                    fligth_number: data.arrival_flight,
                },
                customer: {
                    fullname: data.client,
                    email: data.selected_res.CORREO,
                    phone: data.selected_res.TELEFONO,
                },
                agent: {
                    id: data.agent_id,
                    id_partner: data.partner_id,
                },
            },
        };
    } else {
        return {
            email_to: data.selected_res.CORREO,
            option: "confirm-tr",
            data: {
                confirmation_service: data.selected_res.ID,
                authorization_code: data.selected_res.PAGO,
                search_params: {
                    pickup_location: data.arrival_hotel,
                    destination: data.hotel_destination,
                    n_passengers: data.passengers,
                    trip_type: data.hasdeparture ? "Round Trip" : "One Way",
                    price: data.selected_res.COSTO,
                    transportation: data.transport,
                    comments: data.observations,
                },
                arrival: {
                    date_time: data.arrival_date + " " + data.arrival_time,
                    airline: data.arrival_airline,
                    fligth_number: data.arrival_flight,
                },
                departure: {
                    date_time: data.departure_date + " " + data.departure_time,
                    airline: data.departure_airline,
                    fligth_number: data.departure_flight,
                    hotel_pickup: data.departure_hotel,
                    pickup_at_hotel: data.departure_pickup_time_hotel,
                },
                customer: {
                    fullname: data.client,
                    email: data.selected_res.CORREO,
                    phone: data.selected_res.TELEFONO,
                },
                agent: {
                    id: data.agent_id,
                    id_partner: data.partner_id,
                },
            },
        };
    }
};

export const parserResendEmail = (_data) => {
    // cuando el objeto viene de la base de datos, como json hay que hacerle un JSON.parse()
    console.log(_data.SERVICIO_EXTRA);
    // const parsedService = JSON.parse(_data.SERVICIO_EXTRA);
    debugger;
    let email = {
        email_to: _data.CORREO,
        option: "confirm-tr",
        data: {
            confirmation_service: _data.ID,
            authorization_code: _data.PAGO,
            search_params: {
                pickup_location: _data.HOTEL,
                destination: _data.HOTEL_DESTINO,
                n_passengers: _data.NPASAJEROS,
                trip_type: _data.FECHA_SALIDA !== "N/A" ? "Round Trip" : "One Way",
                price: _data.COSTO,
                transportation: _data.TRANSPORTE,
                comments: _data.COMENTARIOS,
            },
            arrival: {
                date_time: _data.FECHA_LLEGADA + " " + _data.HORA_IN,
                airline: _data.AEROLINEA_IN,
                fligth_number: _data.VUELO_IN,
            },
            customer: {
                fullname: _data.NOMBRE,
                email: _data.CORREO,
                phone: _data.TELEFONO,
            },
            agent: {
                id: _data.AGENTE_ID,
                id_partner: _data.AGENTE_NOMBRE,
            },
        },
    };

    if (_data.FECHA_SALIDA !== "N/A") {
        email.data.departure = {
            date_time: _data.FECHA_SALIDA + " " + _data.HORA_OUT,
            airline: _data.AEROLINEA_OUT,
            fligth_number: _data.VUELO_OUT,
            hotel_pickup: _data.HOTEL_EXTRA || _data.HOTEL_DESTINO,
            pickup_at_hotel: _data.PICKUP_AT_HOTEL,
        };
    }

    if (_data.SERVICIO_EXTRA !== null) {
        email.data.extra_service = {
            name: _data.SERVICIO_EXTRA.name,
            price: _data.SERVICIO_EXTRA.price,
            time: _data.SERVICIO_EXTRA.time,
        };
    }
    console.log(email);
    return email;
};
export const parserInsert = (data, isServiceActive, extra_service_selected) => {
    // crearmos el objeto hardcoeado para que guarde en la db, ya luego lo hacemos dinamico
    // let servicioExtraStatic = {
    //     name:"Stop at the Supermarket or Grocery Store",
    //     price:30
    // }
    return {
        ID: getCodeAux(),
        NOMBRE: data.contact_name,
        APELLIDO: ".",
        CORREO: data.contact_email,
        TELEFONO: data.contact_phone,
        CIUDAD: "'N/A'",
        ESTADO: "'N/A'",
        PAIS: "'N/A'",
        FECHA_REGISTRO: moment().format("DD/MM/YYYY HH:mm"),
        FECHA_LLEGADA: moment(data.arrival_datetime).format("MM/DD/YYYY"),
        HORA_IN: data.arrival_time, /// HORA DE LLEGADA O ARRIVAL O PICKUP
        VUELO_IN: data.arrival_flight,
        AEROLINEA_IN: data.arrival_airline,
        FECHA_SALIDA:
            data.trip_type === "Round Trip" ? moment(data.departure_datetime).format("MM/DD/YYYY") : "N/A",
        HORA_OUT: data.trip_type === "Round Trip" ? data.departure_time : "N/A", ///// HORA DE SALIDA
        VUELO_OUT: data.trip_type === "Round Trip" ? data.departure_flight : "N/A",
        AEROLINEA_OUT: data.trip_type === "Round Trip" ? data.departure_airline : "N/A",
        HOTEL: data.pickup,
        ZONA: data.destination_zone,
        TRANSPORTE: data.transport,
        NPASAJEROS: data.passengers,
        COSTO: parseRate(data.rate),
        MONEDA: "USD",
        COMENTARIOS: data.observations,
        CONFIRMAR: "SI",
        AGENTE_ID: data.agent_id,
        AGENTE_NOMBRE: data.member_id,
        HOTEL_DESTINO: data.destination,
        HOTEL_EXTRA:
            data.trip_type === "Round Trip" || data.hotel_extra === "" || data.hotel_extra === undefined
                ? data.hotel_extra
                : data.destination,
        OBSERVACIONES: data.observations,
        PAGO: data.payment_auth_code,
        ACTIVO: 1,
        SERVICIO_EXTRA: isServiceActive && extra_service_selected ? JSON.stringify(extra_service_selected) : null,
        PICKUP_AT_HOTEL: data.departure_pickup_time_hotel,
    };
};


