const { pool } = require("../database");
const utils = require("./../utils");
const moment = require("moment");

const _TABLE = "RCI_CLIENTES_PRUEBAS";
const _ID = "ID_RESERVATION";

exports.get = (req, res) => {
    let sql;

    if (req.query.date_init && req.query.date_end) {
        const { filter } = req.query;

        let COLUMN = "";

        switch (filter) {
            case "arrival":
                COLUMN = "FECHA_LLEGADA";

                sql = sqlFormat(_TABLE, COLUMN, req.query.date_init, req.query.date_end);
                break;
            case "departure":
                COLUMN = "FECHA_SALIDA";

                sql = sqlFormat(_TABLE, COLUMN, req.query.date_init, req.query.date_end);
                break;
            default:
                COLUMN = "FECHA_REGISTRO";
                sql = `SELECT *  FROM ${_TABLE} 
                        WHERE STR_TO_DATE( ${_TABLE}.${COLUMN}, '%d/%m/%Y') 
                        BETWEEN STR_TO_DATE('${req.query.date_init}', '%d/%m/%Y') 
                        AND STR_TO_DATE('${req.query.date_end}', '%d/%m/%Y') 
                        ORDER BY ${_TABLE}.ID_RESERVATION DESC;`;
                break;
        }
    } else if (req.query.idtr) {
        sql = `SELECT * from ${_TABLE} 
               JOIN CONTROL_TARIFAS ON CONTROL_TARIFAS.ID_RESERVATION = ${_TABLE}.ID_RESERVATION
               WHERE ${_TABLE}.ACTIVO <> 3 and ${_TABLE}.ID = '${req.query.idtr}'`;
    } else {
        sql = `SELECT * from ${_TABLE} WHERE ACTIVO <> 3`;
    }

    // console.log("sql", sql);

    // Ejecutar la consulta
    pool.query(sql, req.query.idtr ? [req.query.idtr] : [], (error, result) => {
        if (error) {
            console.error("Error en la consulta:", error);
            return res.status(500).json({ status: 500, message: "Error en el servidor." });
        }

        const response = {
            status: 200,
            results: utils.userParser(result),
        };
        res.status(200).json(response);
    });
};

const getColumnByFilter = (filter) => {
    switch (filter) {
        case "arrival":
            return "FECHA_LLEGADA";
        case "departure":
            return "FECHA_SALIDA";
        default:
            return "FECHA_REGISTRO";
    }
};

const sqlFormat = (_TABLE, COLUMN, date_init, date_end) => {
    const initDateParsed = parseDate(date_init);
    const endDateParsed = parseDate(date_end);

    return `SELECT * FROM ${_TABLE} 
            WHERE STR_TO_DATE(${_TABLE}.${COLUMN}, '%m/%d/%Y') 
            BETWEEN STR_TO_DATE('${initDateParsed}', '%m/%d/%Y') 
            AND STR_TO_DATE('${endDateParsed}', '%m/%d/%Y') 
            ORDER BY ${_TABLE}.ID_RESERVATION DESC;`;
};

const parseDate = (date) => {
    return moment(date, "DD/MM/YYYY").format("MM/DD/YYYY");
};

exports.getById = (req, res) => {
    const sql = `SELECT * FROM ${_TABLE} WHERE ${_TABLE}.ACTIVO <> 3 AND ${_TABLE}.ID = ?`;

    pool.query(sql, [req.params.id], (error, result) => {
        if (error) {
            console.error("Error en la consulta:", error);
            return res.status(500).json({ status: 500, message: "Error en el servidor." });
        }

        const response = {
            status: 200,
            results: utils.userParser(result),
        };
        res.status(200).json(response);
    });
};

exports.insert = (req, res) => {
    try {
        utils.functionValidate(req.body, (isValid) => {
            if (isValid) {
                const sql = `INSERT INTO ${_TABLE} SET ?`;

                pool.query(sql, req.body, (err, result) => {
                    if (err) {
                        console.error("Error al insertar:", err);
                        return res.status(500).json({ status: 500, message: "Error al insertar el registro." });
                    }

                    const response = {
                        status: 200,
                        results: result,
                    };
                    res.status(200).json(response);
                });
            } else {
                return res.status(405).json({ status: 405, message: "Missing Parameters" });
            }
        });
    } catch (error) {
        console.error("Error en el proceso de inserción:", error);
        return res.status(500).json({ status: 500, message: "Error en el servidor." });
    }
};
exports.update = (req, res) => {
    try {
        utils.functionValidate(req.body, (isValid) => {
            if (isValid) {
                let sql = `UPDATE ${_TABLE} SET `;
                const data = [];

                // Construir la consulta SQL dinámicamente
                const setClauses = Object.keys(req.body).map((key) => {
                    data.push(req.body[key]);
                    return `${key} = ?`;
                });

                sql += setClauses.join(", ") + ` WHERE ${_ID} = ?`;
                data.push(req.params.id);

                // Ejecutar la consulta
                pool.query(sql, data, (err, result) => {
                    if (err) {
                        console.error("Error al actualizar:", err);
                        return res.status(500).json({ status: 500, message: "Error al actualizar el registro." });
                    }

                    const response = {
                        status: 200,
                        results: result,
                    };
                    res.status(200).json(response);
                });
            } else {
                return res.status(405).json({ status: 405, message: "Missing Parameters" });
            }
        });
    } catch (error) {
        console.error("Error en el proceso de actualización:", error);
        return res.status(500).json({ status: 500, message: "Error en el servidor." });
    }
};

exports.delete = (req, res) => {
    try {
        const sql = `UPDATE ${_TABLE} SET ACTIVO = ? WHERE ${_ID} = ?`;
        const data = [3, req.params.id];

        // Ejecutar la consulta
        pool.query(sql, data, (err, result) => {
            if (err) {
                console.error("Error al eliminar:", err);
                return res.status(500).json({ status: 500, message: "Error al eliminar el registro." });
            }

            const response = {
                status: 200,
                results: result,
            };
            res.status(200).json(response);
        });
    } catch (error) {
        console.error("Error en el proceso de eliminación:", error);
        return res.status(500).json({ status: 500, message: "Error en el servidor." });
    }
};