.error-logs-viewer {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.logs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e0e0e0;
}

.logs-header h2 {
    margin: 0;
    color: #333;
}

.logs-actions {
    display: flex;
    gap: 10px;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #545b62;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

.logs-filters {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.filter-row {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.filter-input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    min-width: 150px;
}

.filter-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.logs-container {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.logs-summary {
    padding: 10px 15px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    font-weight: 500;
    color: #666;
}

.logs-list {
    max-height: 600px;
    overflow-y: auto;
}

.log-item {
    padding: 15px;
    border-bottom: 1px solid #e0e0e0;
    cursor: pointer;
    transition: background-color 0.2s;
}

.log-item:hover {
    background-color: #f8f9fa;
}

.log-item:last-child {
    border-bottom: none;
}

.log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.log-timestamp {
    font-size: 12px;
    color: #666;
    font-family: monospace;
}

.log-component {
    font-weight: 500;
    color: #333;
}

.log-status {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.status-red {
    background-color: #fee;
    color: #c53030;
}

.status-orange {
    background-color: #fff3cd;
    color: #856404;
}

.status-blue {
    background-color: #e3f2fd;
    color: #1565c0;
}

.status-gray {
    background-color: #f5f5f5;
    color: #666;
}

.log-message {
    color: #666;
    font-size: 14px;
    margin-bottom: 4px;
}

.log-endpoint {
    font-size: 12px;
    color: #999;
    font-family: monospace;
}

/* Modal */
.log-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.log-modal {
    background-color: white;
    border-radius: 8px;
    max-width: 80vw;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #e0e0e0;
    background-color: #f8f9fa;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.modal-header button {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #666;
}

.modal-content {
    padding: 20px;
    overflow: auto;
    max-height: 60vh;
}

.modal-content pre {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    overflow: auto;
    font-size: 12px;
    line-height: 1.4;
    margin: 0;
}

/* Responsive */
@media (max-width: 768px) {
    .error-logs-viewer {
        padding: 10px;
    }
    
    .logs-header {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }
    
    .filter-row {
        flex-direction: column;
    }
    
    .filter-input {
        min-width: auto;
    }
    
    .log-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .log-modal {
        max-width: 95vw;
        max-height: 95vh;
    }
}
