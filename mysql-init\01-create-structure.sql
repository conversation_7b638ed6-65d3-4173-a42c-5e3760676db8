-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Servidor: localhost:3306
-- Tiempo de generación: 28-07-2025 a las 18:36:07
-- Versión del servidor: 5.7.23-23
-- Versión de PHP: 8.1.33

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Base de datos: `transrou_rci-reservations`
--

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `CONTROL_TARIFAS`
--

CREATE TABLE `CONTROL_TARIFAS` (
  `ID_CONTROL_TARIFAS` int(11) NOT NULL,
  `ID_RESERVATION` int(11) NOT NULL,
  `DESCUENTO` int(11) NOT NULL DEFAULT '0',
  `TOTAL` float NOT NULL,
  `TOTAL_DESCUENTO` float NOT NULL,
  `ACTIVO` int(11) NOT NULL DEFAULT '1',
  `CREADO` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `RCI_CLIENTES_PRUEBAS`
--

CREATE TABLE `RCI_CLIENTES_PRUEBAS` (
  `ID_RESERVATION` int(11) NOT NULL,
  `ID` varchar(15) DEFAULT NULL,
  `NOMBRE` varchar(80) DEFAULT NULL,
  `APELLIDO` varchar(80) DEFAULT NULL,
  `CORREO` varchar(80) DEFAULT NULL,
  `TELEFONO` varchar(12) DEFAULT NULL,
  `CIUDAD` varchar(80) DEFAULT NULL,
  `ESTADO` varchar(80) DEFAULT NULL,
  `PAIS` varchar(80) DEFAULT NULL,
  `FECHA_REGISTRO` varchar(20) DEFAULT NULL,
  `FECHA_LLEGADA` varchar(20) DEFAULT NULL,
  `HORA_IN` varchar(8) DEFAULT NULL,
  `VUELO_IN` varchar(20) DEFAULT NULL,
  `AEROLINEA_IN` varchar(80) DEFAULT NULL,
  `FECHA_SALIDA` varchar(10) DEFAULT NULL,
  `HORA_OUT` varchar(8) DEFAULT NULL,
  `VUELO_OUT` varchar(20) DEFAULT NULL,
  `AEROLINEA_OUT` varchar(80) DEFAULT NULL,
  `HOTEL` varchar(80) DEFAULT NULL,
  `ZONA` varchar(80) DEFAULT NULL,
  `TRANSPORTE` varchar(20) DEFAULT NULL,
  `NPASAJEROS` int(11) DEFAULT NULL,
  `COSTO` float DEFAULT NULL,
  `MONEDA` varchar(3) DEFAULT NULL,
  `COMENTARIOS` varchar(500) DEFAULT NULL,
  `CONFIRMAR` varchar(6) DEFAULT NULL,
  `AGENTE_ID` varchar(150) DEFAULT NULL,
  `AGENTE_NOMBRE` varchar(20) DEFAULT NULL,
  `HOTEL_DESTINO` varchar(150) DEFAULT NULL,
  `HOTEL_EXTRA` varchar(145) DEFAULT NULL,
  `OBSERVACIONES` varchar(500) DEFAULT NULL,
  `PAGO` varchar(50) DEFAULT NULL,
  `ACTIVO` int(11) DEFAULT '1',
  `SERVICIO_EXTRA` json DEFAULT NULL,
  `PICKUP_AT_HOTEL` varchar(8) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `USUARIOS`
--

CREATE TABLE `USUARIOS` (
  `ID_USUARIO` int(11) NOT NULL,
  `PLATAFORMA` varchar(145) NOT NULL DEFAULT 'RCI',
  `CORREO` varchar(145) NOT NULL,
  `USUARIO` varchar(100) NOT NULL,
  `CONTRA` varchar(50) NOT NULL,
  `NIVEL` int(11) NOT NULL,
  `ACTIVO` int(11) NOT NULL DEFAULT '1',
  `CREADO` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Índices para tablas volcadas
--

--
-- Indices de la tabla `CONTROL_TARIFAS`
--
ALTER TABLE `CONTROL_TARIFAS`
  ADD PRIMARY KEY (`ID_CONTROL_TARIFAS`);

--
-- Indices de la tabla `RCI_CLIENTES_PRUEBAS`
--
ALTER TABLE `RCI_CLIENTES_PRUEBAS`
  ADD PRIMARY KEY (`ID_RESERVATION`);

--
-- Indices de la tabla `USUARIOS`
--
ALTER TABLE `USUARIOS`
  ADD PRIMARY KEY (`ID_USUARIO`);

--
-- AUTO_INCREMENT de las tablas volcadas
--

--
-- AUTO_INCREMENT de la tabla `CONTROL_TARIFAS`
--
ALTER TABLE `CONTROL_TARIFAS`
  MODIFY `ID_CONTROL_TARIFAS` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `RCI_CLIENTES_PRUEBAS`
--
ALTER TABLE `RCI_CLIENTES_PRUEBAS`
  MODIFY `ID_RESERVATION` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `USUARIOS`
--
ALTER TABLE `USUARIOS`
  MODIFY `ID_USUARIO` int(11) NOT NULL AUTO_INCREMENT;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
