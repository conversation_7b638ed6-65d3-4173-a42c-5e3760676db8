# rci-booking-form

## Desarrollo con Docker y recarga automática

Para trabajar en modo desarrollo y que los cambios en el código se reflejen automáticamente en el navegador, sigue estos pasos:

### 1. Requisitos previos
- <PERSON><PERSON> y Docker Compose instalados.

### 2. Levantar el entorno de desarrollo

Ejecuta en la terminal, dentro de la carpeta del proyecto:

```
docker compose up
```

Esto hará lo siguiente:
- Montará tu código local dentro del contenedor (`.:/app`)
- Usará `CHOKIDAR_USEPOLLING=true` para que React detecte cambios en archivos
- Expondrá la app en [http://localhost:3000](http://localhost:3000)

### 3. Recarga automática

Cada vez que guardes cambios en los archivos fuente, la app se recargará automáticamente en el navegador.

### 4. Notas
- Si tienes problemas con la recarga, asegúrate de que los archivos se editan dentro de la carpeta sincronizada.
- Para detener el entorno, presiona `Ctrl+C` en la terminal o usa:
  ```
  docker compose down
  ```

---

## Build de producción

Para generar el build estático y desplegarlo en un hosting tradicional:

1. Ejecuta:
   ```
   npm run build
   ```
2. Sube el contenido de la carpeta `build` a tu servidor (Apache, Nginx, etc.)

---

## Despliegue de build estático con Docker (Nginx)

1. Genera el build:
   ```
   npm run build
   ```
2. Crea este `Dockerfile` en la raíz del proyecto:
   ```dockerfile
   FROM nginx:alpine
   COPY build/ /usr/share/nginx/html
   EXPOSE 80
   CMD ["nginx", "-g", "daemon off;"]
   ```
3. Construye y ejecuta:
   ```
   docker build -t mi-app-estatica .
   docker run -p 80:80 mi-app-estatica
   ```

¡Listo!
