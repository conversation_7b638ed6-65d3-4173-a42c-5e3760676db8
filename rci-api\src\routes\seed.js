const express = require('express');
const router = express.Router();
const seedController = require('../controllers/seed_controller');

// Endpoint para hacer seed de la base de datos de producción a desarrollo
router.post('/seed-db', seedController.seedDatabase);

// Endpoint para obtener información de las tablas
router.get('/tables-info', seedController.getTablesInfo);

// Endpoint para limpiar todas las tablas locales
router.delete('/clear-tables', seedController.clearLocalTables);

// Endpoint para crear tablas locales con la misma estructura que producción
router.post('/create-tables', seedController.createTablesFromProduction);

// Endpoint para obtener información sobre los filtros configurados
router.get('/filters-info', seedController.getFiltersInfo);

// Endpoint para comparar estructuras de tablas
router.get('/compare-tables/:tableName?', seedController.compareTableStructures);

module.exports = router;
