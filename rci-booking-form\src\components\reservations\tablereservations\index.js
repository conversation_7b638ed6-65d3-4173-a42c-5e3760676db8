import React, { Fragment, useMemo } from "react";
import ReactDatatable from "@ashvin27/react-datatable";
import * as XLSX from 'xlsx';
import * as FileSaver from 'file-saver';
import "font-awesome/css/font-awesome.min.css";

const TableReservations = ({
    records,
    handler,
    editHandler,
    sendReservationEmail,
    userlvl,
}) => {
    const columns = useMemo(() => [
        {
            key: "ID",
            text: "ID Reserva",
            align: "center",
            sortable: true,
        },
        {
            key: "REGISTRATION_DATE",
            text: "Registro",
            align: "center",
            sortable: true,
        },
        {
            key: "AGENTE_ID",
            text: "ID Agente",
            align: "center",
            sortable: true,
        },
        {
            key: "NAME",
            text: "Cliente",
            align: "center",
            sortable: true,
        },
        {
            key: "ARRIVAL_DATE",
            text: "Llegada",
            align: "center",
            sortable: true,
        },
        {
            key: "ARRIVAL_FLIGHT",
            text: "Vuelo",
            align: "center",
            sortable: true,
        },
        {
            key: "DEPARTURE_DATE",
            text: "Salida",
            align: "center",
            sortable: true,
        },
        {
            key: "TRANSPORT",
            text: "Transporte",
            align: "center",
            sortable: true,
        },
        {
            key: "AUTH_CODE",
            text: "Auth Code",
            align: "center",
            sortable: true,
        },
        {
            key: "action",
            text: "Opciones",
            width: 125,
            align: "center",
            className: "action",
            sortable: false,
            cell: (record) => (
                <Fragment key={record.ID}>
                    <button
                        className="btn btn-dark btn-sm"
                        style={{ marginRight: "4px" }}
                        onClick={() => handler(record)}
                    >
                        <i className="fa fa-info-circle"></i>
                    </button>

                    {userlvl === "1" ? (
                        <button
                            onClick={() => editHandler(record)}
                            className="btn btn-info btn-sm"
                            style={{ marginRight: "4px" }}
                        >
                            <i className="fa fa-edit"></i>
                        </button>
                    ) : (
                        <div></div>
                    )}

                    <button
                        onClick={() => sendReservationEmail(record, false)}
                        className="btn btn-warning btn-sm"
                        style={{ marginRight: "4px" }}
                    >
                        <i className="fa fa-paper-plane"></i>
                    </button>

                    <button
                        onClick={() => sendReservationEmail(record, true)}
                        className="btn btn-danger btn-sm resend"
                        style={{ marginRight: "4px" }}
                    >
                        <i className="fa fa-paper-plane"></i>
                    </button>
                </Fragment>
            ),
        },
    ], [handler, editHandler, sendReservationEmail, userlvl]);

    const config = useMemo(() => ({
        page_size: 20,
        length_menu: [20, 50, 100, 200],
        button: { excel: false },
        sort: { column: "ID_RESERVATION", order: "desc" },
    }), []);

    // For debugging, keep the console.log if needed
    // console.log({ records, handler, editHandler, sendReservationEmail, userlvl });

    const exportExcel = () => {
        const ws = XLSX.utils.json_to_sheet(records);
        const wb = { Sheets: { data: ws }, SheetNames: ['data'] };
        const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
        const blob = new Blob(
            [excelBuffer],
            { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8;' }
        );
        FileSaver.saveAs(blob, 'reservations.xlsx');
    };

    return (<>
        <div className="medium-4 cell d-flex justify-content-end align-items-end">
            <button type="button" className="button btn-green" onClick={exportExcel}>Export to Excell</button>
        </div>
        <ReactDatatable
            className="table stack b-white hover table-bordered"
            key={"table11"}
            config={config}
            records={records}
            columns={columns}
            />
    </>
    );
};

export default TableReservations;
