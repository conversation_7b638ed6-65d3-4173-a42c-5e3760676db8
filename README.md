# rci-booking-callcenter

Este monorepo contiene dos proyectos principales:

- **Frontend:** ReactJS (`rci-booking-form`)
- **Backend:** NodeJS (`rci-api`)

## Desarrollo local con Docker Compose

1. Asegúrate de tener Docker y Docker Compose instalados.
2. <PERSON><PERSON> la raíz del proyecto, ejecuta:

```sh
docker-compose up
```

Esto levantará tres servicios:
- **Frontend:** http://localhost:3000
- **Backend:** http://localhost:4000
- **MySQL:** puerto 3307 en tu máquina (3306 dentro de Docker)

### Autorecarga de código
- El backend usa `nodemon` con autorecarga y configuración especial para Docker.
- El frontend tiene hot reload por defecto.

### Base de datos
- El contenedor MySQL expone el puerto 3307 para evitar conflicto con instalaciones nativas.
- El backend se conecta automáticamente al servicio MySQL usando el host `mysql` y puerto `3306` (no necesitas cambiar nada en el backend).

### Inicialización automática de datos
El sistema incluye **inicialización automática** que se ejecuta **solo una vez** al crear los contenedores:
- **Crea las estructuras de tablas** automáticamente desde `db/db.sql`
- **Importa los últimos 100 registros** de las tablas principales (`RCI_CLIENTES_PRUEBAS` y `CONTROL_TARIFAS`)
- **Se ejecuta solo al crear el contenedor MySQL** por primera vez, no en cada reinicio

#### Endpoints de gestión de datos disponibles:
- `POST /api/v1/seed-db` - Ejecuta el seed completo (crea estructura + importa datos)
- `GET /api/v1/tables-info` - Muestra información de las tablas
- `POST /api/v1/create-tables` - Solo crea las estructuras de tablas
- `DELETE /api/v1/clear-tables` - Limpia todas las tablas locales
- `GET /api/v1/filters-info` - Muestra configuración de filtros de fecha
- `GET /api/v1/compare-tables/:tableName` - Compara estructuras entre producción y desarrollo

#### Configuración de filtros
El sistema incluye filtros inteligentes para importar solo datos relevantes:
- **RCI_CLIENTES_PRUEBAS**: Los últimos 100 registros (ordenados por `ID_RESERVATION` DESC)
- **CONTROL_TARIFAS**: Los últimos 100 registros (ordenados por `ID_CONTROL_TARIFAS` DESC)
- **Criterio de orden**: Se usan columnas autoincrementales (`ID_RESERVATION`, `ID_CONTROL_TARIFAS`) para obtener los registros más recientes

### Migrar datos desde tu MySQL local (alternativo)
Si prefieres usar tu base de datos local en lugar del seed automático:

```sh
mysqldump -u root -p -h 127.0.0.1 -P 3306 transrou_rci-dev > backup.sql
mysql -u root -p -h 127.0.0.1 -P 3307 transrou_rci-dev < backup.sql
```

> Cambia los nombres de usuario, base y puertos según tu configuración si es necesario.

## Estructura del proyecto

```
rci-booking-callcenter/
├── rci-api/            # Backend NodeJS
│   └── Dockerfile
├── rci-booking-form/   # Frontend ReactJS
│   └── Dockerfile
├── docker-compose.yml  # Orquestador de servicios
└── README.md           # Este archivo
```

## Requisitos
- Docker
- Docker Compose
- **Acceso a la base de datos de producción** (para el seed automático)

### Compatibilidad
- ✅ **Windows** (con Docker Desktop)
- ✅ **macOS** (con Docker Desktop for Mac)
- ✅ **Linux** (Docker nativo)

> **Nota para macOS**: Todos los comandos shell y Docker son totalmente compatibles. Asegúrate de tener Docker Desktop ejecutándose.

## Variables de entorno requeridas
Para que el seed automático funcione, copia el archivo de ejemplo y configura las variables:

```sh
cp rci-api/.env.example rci-api/.env
```

Luego edita `rci-api/.env` con tus datos de producción:

```env
# Base de datos de producción (para seed)
PROD_DB_HOST=tu-servidor-produccion.com
PROD_DB_USER=tu-usuario-produccion
PROD_DB_PASSWORD=tu-password-produccion
PROD_DB_NAME=transrou_rci-reservations

# Base de datos de desarrollo (local) - NO CAMBIAR
DEV_DB_HOST=mysql
DEV_DB_USER=root
DEV_DB_PASSWORD=root
DEV_DB_NAME=transrou_rci-dev
ENVIRONMENT=DEVELOPMENT
```

## Comando de inicialización automática
Al ejecutar `docker-compose up` **por primera vez**:
1. **MySQL ejecuta automáticamente** `db/db.sql` para crear la estructura
2. **Se conecta a la base de datos de producción** (para el seed)
3. **Importa automáticamente** los últimos 100 registros de las tablas principales
4. **En reinicios posteriores** no vuelve a ejecutar la inicialización

Si necesitas ejecutar el seed manualmente:
```sh
curl -X POST http://localhost:4000/api/v1/seed-db
```

Para **forzar reinicialización completa**:
```sh
docker-compose down -v  # Elimina volúmenes
docker-compose up -d    # Recrea desde cero
```

## Notas
- El backend y frontend recargan automáticamente al guardar cambios.
- El backend usa `nodemon` y variables para forzar autorecarga en Docker.
- El contenedor MySQL guarda los datos en un volumen persistente (`mysql_data`).
- **El seed se ejecuta solo en entorno DEVELOPMENT** por seguridad.
- Los datos se filtran por fecha para mantener un tamaño manejable de la base de datos local.

---

¿Dudas o sugerencias? Abre un issue en el repositorio.
