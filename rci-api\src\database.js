const mysql = require("mysql2");

// Configuración de la base de datos
const dbConfig = {
    host: process.env.ENVIRONMENT === "PRODUCTION" ? process.env.DB_HOST : process.env.DEV_DB_HOST,
    database: process.env.ENVIRONMENT === "PRODUCTION" ? process.env.DB_NAME : process.env.DEV_DB_NAME,
    user: process.env.ENVIRONMENT === "PRODUCTION" ? process.env.DB_USER : process.env.DEV_DB_USER,
    password: process.env.ENVIRONMENT === "PRODUCTION" ? process.env.DB_PASS : process.env.DEV_DB_PASS,
    connectionLimit: 10 // Número máximo de conexiones en el pool
};

// Validar si las variables de entorno necesarias están definidas
if (process.env.ENVIRONMENT === "PRODUCTION") {
    if (!process.env.DB_HOST || !process.env.DB_NAME || !process.env.DB_USER || !process.env.DB_PASS) {
        throw new Error("Faltan variables de entorno para la conexión a la base de datos en producción. Asegúrate de definir DB_HOST, DB_NAME, DB_USER y DB_PASS.");
    }
} else {
    if (!process.env.DEV_DB_HOST || !process.env.DEV_DB_NAME || !process.env.DEV_DB_USER) {
        throw new Error("Faltan variables de entorno para la conexión a la base de datos en desarrollo. Asegúrate de definir DEV_DB_HOST, DEV_DB_NAME, DEV_DB_USER y DEV_DB_PASS.");
    }
}

// Comprobar el entorno
if (process.env.ENVIRONMENT !== "PRODUCTION") {
    console.log("Conexión a la base de datos en entorno de desarrollo...");
} else {
    console.log("Conexión a la base de datos en entorno de producción...");
}

// Crear el pool de conexiones
const pool = mysql.createPool(dbConfig);

// Manejo de errores en la conexión
pool.getConnection((err, connection) => {
    if (err) {
        console.error("Error al conectar a la base de datos:", err);
        return;
    }
    console.log("Conectado a la base de datos como ID de conexión:", connection.threadId);
    connection.release(); // Liberar la conexión de vuelta al pool
});

module.exports = { pool };