require("dotenv").config();

const express = require("express");
const bodyParser = require("body-parser");
const methodOverride = require("method-override");
const compression = require("compression");
const cors = require("cors");

// Initialize Express app
const app = express();
const apiVersion = "/api/v1";

// Set port
const PORT = process.env.PORT || 8000;
app.set("port", PORT);

// Middleware
app.use(compression());
app.use(cors());
app.use(bodyParser.json({ limit: "50mb" }));
app.use(bodyParser.urlencoded({ limit: "50mb", extended: true }));
app.use(methodOverride());

// Database connection
require("./src/database");

// Routes
const authRoutes = require("./src/routes/auth");
const emailRoutes = require("./src/routes/email");
const errorLogsRoutes = require("./src/routes/error_logs");
const ratesRoutes = require("./src/routes/rates");
const ratesControlRoutes = require("./src/routes/rates_control");
const reservationRoutes = require("./src/routes/reservation");
const seedRoutes = require("./src/routes/seed");
const serviceExtraRoutes = require("./src/routes/service_extra");
const usersRoutes = require("./src/routes/users");

// Routes array for easier management
const routes = [
    authRoutes,
    emailRoutes,
    errorLogsRoutes,
    ratesRoutes,
    ratesControlRoutes,
    reservationRoutes,
    seedRoutes,
    serviceExtraRoutes,
    usersRoutes
];

// Health check endpoint
app.get("/", (req, res) => {
    res.json({
        message: "RCI Booking API is running",
        version: "1.0.0",
        environment: process.env.ENVIRONMENT || "DEVELOPMENT"
    });
});

routes.forEach(route => app.use(apiVersion, route));

// Start server
app.listen(PORT, () => {
    console.log(`Running API server on port ${PORT}`);
    console.log(`Seed endpoints available at /api/v1/seed-db, /api/v1/tables-info, /api/v1/clear-tables, /api/v1/create-tables, /api/v1/filters-info, /api/v1/compare-tables`);
    console.log(`Date format corrected to %d/%m/%Y for RCI_CLIENTES_PRUEBAS`);
    
    // Mensaje informativo sobre inicialización
    if (process.env.ENVIRONMENT === 'DEVELOPMENT') {
        console.log(`\n💡 Para inicializar la base de datos con datos de producción:`);
        console.log(`   curl -X POST http://localhost:4000/api/v1/seed-db`);
        console.log(`🔧 Endpoints disponibles para gestión de datos:`);
        console.log(`   - POST /api/v1/seed-db (seed completo)`);
        console.log(`   - POST /api/v1/create-tables (solo estructuras)`);
        console.log(`   - DELETE /api/v1/clear-tables (limpiar datos)`);
        console.log(`   - GET /api/v1/tables-info (información de tablas)`);
        console.log(`   - GET /api/v1/filters-info (configuración de filtros)`);
    }
});
