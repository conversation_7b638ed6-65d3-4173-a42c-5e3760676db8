# RCI Booking API

API REST para el sistema de reservas RCI con funcionalidades de seed automático desde base de datos de producción.

## Configuración inicial

### 1. Variables de entorno
Copia el archivo de ejemplo y configura las variables:

```bash
cp .env.example .env
```

Edita el archivo `.env` con tus datos:

```env
# Base de datos de producción (para seed)
PROD_DB_HOST=tu-servidor-produccion.com
PROD_DB_USER=tu-usuario-produccion
PROD_DB_PASSWORD=tu-password-produccion
PROD_DB_NAME=transrou_rci-reservations

# Base de datos de desarrollo (local) - NO CAMBIAR
DEV_DB_HOST=mysql
DEV_DB_USER=root
DEV_DB_PASSWORD=root
DEV_DB_NAME=transrou_rci-dev
ENVIRONMENT=DEVELOPMENT

# Puerto del servidor
PORT=4000
```

### 2. Instalación y ejecución

```bash
# Instalar dependencias
npm install

# Ejecutar en desarrollo
npm run dev
```

## Sistema de Seed Automático

### Configuración de filtros
El sistema importa solo los datos más relevantes:
- **RCI_CLIENTES_PRUEBAS**: Los últimos 100 registros (ordenados por `ID_RESERVATION` DESC)
- **CONTROL_TARIFAS**: Los últimos 100 registros (ordenados por `ID_CONTROL_TARIFAS` DESC)

### Endpoints disponibles

#### Gestión de datos
- `POST /api/v1/seed-db` - Ejecuta el seed completo (crea estructura + importa datos)
- `POST /api/v1/create-tables` - Solo crea las estructuras de tablas
- `DELETE /api/v1/clear-tables` - Limpia todas las tablas locales
- `GET /api/v1/tables-info` - Muestra información de las tablas
- `GET /api/v1/filters-info` - Muestra configuración de filtros
- `GET /api/v1/compare-tables/:tableName` - Compara estructuras entre producción y desarrollo

#### Health check
- `GET /` - Estado del API

## Prueba completa desde cero

Para simular la experiencia de alguien que recién descarga el repositorio:

```bash
# 1. Bajar contenedores y limpiar volúmenes (desde el directorio raíz del proyecto)
docker-compose down -v

# 2. Copiar archivo de variables de entorno
cp rci-api/.env.example rci-api/.env

# 3. Editar el archivo .env con tus datos de producción
# (Edita rci-api/.env con tu editor preferido)

# 4. Levantar los contenedores desde cero
docker-compose up -d

# 5. Verificar que el backend esté funcionando
curl -X GET http://localhost:4000/

# 6. Ejecutar el seed para importar los últimos 100 registros
curl -X POST http://localhost:4000/api/v1/seed-db

# 7. Verificar que se importaron los datos
curl -X GET http://localhost:4000/api/v1/tables-info
```

## Estructura del proyecto

```
rci-api/
├── src/
│   ├── controllers/     # Controladores de la API
│   ├── routes/         # Definición de rutas
│   ├── database.js     # Configuración de base de datos
│   └── utils.js        # Utilidades
├── .env.example        # Ejemplo de variables de entorno
├── app.js             # Punto de entrada de la aplicación
└── package.json       # Dependencias y scripts
```

## Notas importantes

- El seed **NO se ejecuta automáticamente** al iniciar el servidor
- Solo funciona en entorno `DEVELOPMENT` por seguridad
- Los datos se filtran por ID autoincremental para obtener los registros más recientes
- Requiere acceso a la base de datos de producción para funcionar